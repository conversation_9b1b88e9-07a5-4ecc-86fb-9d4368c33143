# 🌌 COSMIC HVAC CRM - Environment Configuration
# Copy this file to .env and configure your values

# 🚀 API Configuration
VITE_API_BASE_URL=http://localhost:8080
VITE_WS_BASE_URL=ws://localhost:8080

# 🔐 Authentication
VITE_AUTH_DOMAIN=your-auth-domain.com
VITE_AUTH_CLIENT_ID=your-client-id

# 🎨 Application Settings
VITE_APP_NAME="Cosmic HVAC CRM"
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# 🌟 Feature Flags
VITE_ENABLE_REAL_TIME=true
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_OFFLINE_MODE=false

# 📊 Analytics & Monitoring
VITE_ANALYTICS_ID=your-analytics-id
VITE_SENTRY_DSN=your-sentry-dsn

# 🎯 Performance Settings
VITE_API_TIMEOUT=30000
VITE_CACHE_TIME=300000
VITE_STALE_TIME=120000

# 🔧 Development Settings
VITE_DEBUG_MODE=true
VITE_MOCK_API=false
VITE_LOG_LEVEL=debug
