import { defineConfig } from 'vite'
import solid from 'vite-plugin-solid'

export default defineConfig({
  plugins: [solid()],
  server: {
    port: 3000,
    host: true,
    proxy: {
      // Proxy API calls to GoBackend-Kratos
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
      },
      // Proxy WebSocket connections
      '/ws': {
        target: 'ws://localhost:8080',
        ws: true,
        changeOrigin: true,
      }
    }
  },
  build: {
    target: 'esnext',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['solid-js', '@solidjs/router'],
          api: ['@trpc/client', '@tanstack/solid-query'],
          ui: ['lucide-solid'],
        }
      }
    }
  },
  define: {
    // Expose environment variables
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
  }
})
