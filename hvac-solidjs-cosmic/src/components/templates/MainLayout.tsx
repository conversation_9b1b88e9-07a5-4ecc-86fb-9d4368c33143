import { type Component, type JSX, createSignal, onMount } from 'solid-js'
import { A, useLocation } from '@solidjs/router'
import { 
  Home, 
  BarChart3, 
  Users, 
  Wrench, 
  Package, 
  TrendingUp, 
  Settings,
  Menu,
  X
} from 'lucide-solid'

interface MainLayoutProps {
  children: JSX.Element
}

export const MainLayout: Component<MainLayoutProps> = (props) => {
  const [isSidebarOpen, setIsSidebarOpen] = createSignal(false)
  const [isLoaded, setIsLoaded] = createSignal(false)
  const location = useLocation()

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 100)
  })

  const navigation = [
    { name: 'Home', href: '/', icon: Home },
    { name: 'Dashboard', href: '/dashboard', icon: BarChart3 },
    { name: 'Customers', href: '/customers', icon: Users },
    { name: 'Service Orders', href: '/service-orders', icon: Wrench },
    { name: 'Inventory', href: '/inventory', icon: Package },
    { name: 'Analytics', href: '/analytics', icon: TrendingUp },
    { name: 'Settings', href: '/settings', icon: Settings },
  ]

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(href)
  }

  return (
    <div class="min-h-screen bg-gradient-to-br from-cosmic-500 via-divine-500 to-golden-500 relative">
      {/* Cosmic Background Effects */}
      <div class="absolute inset-0 pointer-events-none">
        {/* Floating Orbs */}
        {Array.from({ length: 15 }, (_, index) => (
          <div
            class="absolute rounded-full bg-white/5 backdrop-blur-sm animate-pulse"
            style={{
              width: `${Math.random() * 80 + 20}px`,
              height: `${Math.random() * 80 + 20}px`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              'animation-delay': `${index * 0.3}s`,
              'animation-duration': `${4 + Math.random() * 3}s`
            }}
          />
        ))}

        {/* Golden Ratio Spiral */}
        <div class="absolute top-1/4 right-1/4 transform opacity-5">
          <svg width="300" height="300" viewBox="0 0 300 300">
            <path
              d="M150,150 Q225,150 225,75 Q225,0 150,0 Q75,0 75,75 Q75,150 150,150"
              fill="none"
              stroke="white"
              stroke-width="1"
              class="animate-pulse"
            />
          </svg>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      <div
        class={`fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden transition-opacity duration-300 ${
          isSidebarOpen() ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={() => setIsSidebarOpen(false)}
      />

      {/* Sidebar */}
      <div
        class={`fixed inset-y-0 left-0 z-50 w-64 bg-white/10 backdrop-blur-lg border-r border-white/20 transform transition-transform duration-300 ease-out ${
          isSidebarOpen() ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
        }`}
      >
        <div class="flex flex-col h-full">
          {/* Logo */}
          <div class="flex items-center justify-between p-golden-md border-b border-white/20">
            <div class="flex items-center space-x-golden-sm">
              <div class="w-8 h-8 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">H</span>
              </div>
              <span class="text-white font-bold text-lg">HVAC CRM</span>
            </div>
            <button
              class="lg:hidden text-white/60 hover:text-white transition-colors"
              onClick={() => setIsSidebarOpen(false)}
            >
              <X size={20} />
            </button>
          </div>

          {/* Navigation */}
          <nav class="flex-1 p-golden-sm space-y-golden-xs">
            {navigation.map((item) => {
              const Icon = item.icon
              return (
                <A
                  href={item.href}
                  class={`flex items-center space-x-golden-sm px-golden-sm py-golden-sm rounded-lg transition-all duration-200 ${
                    isActive(item.href)
                      ? 'bg-white/20 text-white shadow-lg'
                      : 'text-white/70 hover:text-white hover:bg-white/10'
                  }`}
                  onClick={() => setIsSidebarOpen(false)}
                >
                  <Icon size={20} />
                  <span class="font-medium">{item.name}</span>
                </A>
              )
            })}
          </nav>

          {/* Footer */}
          <div class="p-golden-md border-t border-white/20">
            <div class="text-white/60 text-sm text-center">
              <p>Cosmic CRM v1.0</p>
              <p class="text-xs mt-1">Powered by 137 truths</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div class="lg:pl-64">
        {/* Top Bar */}
        <header class="bg-white/10 backdrop-blur-lg border-b border-white/20 sticky top-0 z-30">
          <div class="flex items-center justify-between px-golden-md py-golden-sm">
            <button
              class="lg:hidden text-white/60 hover:text-white transition-colors"
              onClick={() => setIsSidebarOpen(true)}
            >
              <Menu size={24} />
            </button>
            
            <div class="flex items-center space-x-golden-md">
              <h1 class="text-white font-semibold text-lg">
                {navigation.find(nav => isActive(nav.href))?.name || 'HVAC CRM'}
              </h1>
            </div>

            <div class="flex items-center space-x-golden-sm">
              <div class="w-8 h-8 bg-gradient-to-r from-golden-400 to-cosmic-400 rounded-full flex items-center justify-center">
                <span class="text-white font-bold text-sm">U</span>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main
          class={`relative z-10 transition-all duration-1000 ${
            isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
          }`}
        >
          {props.children}
        </main>
      </div>
    </div>
  )
}
