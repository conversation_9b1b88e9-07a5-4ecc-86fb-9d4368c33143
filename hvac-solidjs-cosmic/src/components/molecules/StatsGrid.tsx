import { type Component, type JSX, For } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { TrendingUp, TrendingDown, Minus } from 'lucide-solid'

export interface StatItem {
  id: string
  title: string
  value: string | number
  change?: {
    value: string | number
    type: 'increase' | 'decrease' | 'neutral'
    period?: string
  }
  icon?: Component<{ size: number; class?: string }>
  color?: 'cosmic' | 'golden' | 'divine' | 'glass'
  description?: string
  trend?: number[] // Array of values for mini trend chart
  format?: 'number' | 'currency' | 'percentage' | 'time'
  precision?: number
}

export interface StatsGridProps {
  stats: StatItem[]
  columns?: 1 | 2 | 3 | 4 | 5 | 6
  size?: 'sm' | 'md' | 'lg'
  animated?: boolean
  class?: string
}

export const StatsGrid: Component<StatsGridProps> = (props) => {
  const columns = () => props.columns || 4
  const size = () => props.size || 'md'

  const getGridClasses = () => {
    const colClasses = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
    }
    return `grid ${colClasses[columns()]} gap-golden-md`
  }

  const formatValue = (value: string | number, format?: string, precision?: number) => {
    if (typeof value === 'string') return value

    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: precision || 0,
          maximumFractionDigits: precision || 0
        }).format(value)
      
      case 'percentage':
        return `${value.toFixed(precision || 1)}%`
      
      case 'number':
        return new Intl.NumberFormat('en-US', {
          minimumFractionDigits: precision || 0,
          maximumFractionDigits: precision || 0
        }).format(value)
      
      case 'time':
        // Assume value is in minutes
        const hours = Math.floor(value / 60)
        const minutes = value % 60
        return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`
      
      default:
        return value.toLocaleString()
    }
  }

  const getTrendIcon = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return TrendingUp
      case 'decrease':
        return TrendingDown
      default:
        return Minus
    }
  }

  const getTrendColor = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return 'text-green-400'
      case 'decrease':
        return 'text-red-400'
      default:
        return 'text-white/60'
    }
  }

  const renderMiniChart = (trend: number[]) => {
    if (!trend || trend.length < 2) return null

    const max = Math.max(...trend)
    const min = Math.min(...trend)
    const range = max - min || 1

    const points = trend.map((value, index) => {
      const x = (index / (trend.length - 1)) * 60
      const y = 20 - ((value - min) / range) * 20
      return `${x},${y}`
    }).join(' ')

    return (
      <div class="mt-golden-sm">
        <svg width="60" height="20" class="opacity-60">
          <polyline
            points={points}
            fill="none"
            stroke="currentColor"
            stroke-width="1.5"
            class="text-cosmic-400"
          />
        </svg>
      </div>
    )
  }

  return (
    <div class={`${getGridClasses()} ${props.class || ''}`}>
      <For each={props.stats}>
        {(stat, index) => {
          const Icon = stat.icon
          const TrendIcon = stat.change ? getTrendIcon(stat.change.type) : null

          return (
            <CosmicCard
              variant={stat.color || 'glass'}
              size={size()}
              glow
              hover3d={props.animated}
              physics={props.animated}
              class={props.animated ? `transition-all duration-300 delay-${index() * 100}` : ''}
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  {/* Title */}
                  <p class="text-white/70 text-sm mb-golden-xs font-medium">
                    {stat.title}
                  </p>

                  {/* Value */}
                  <div class="flex items-baseline space-x-golden-sm mb-golden-xs">
                    <span class="text-2xl lg:text-3xl font-bold text-white">
                      {formatValue(stat.value, stat.format, stat.precision)}
                    </span>
                  </div>

                  {/* Change Indicator */}
                  {stat.change && (
                    <div class={`flex items-center space-x-golden-xs text-sm ${getTrendColor(stat.change.type)}`}>
                      {TrendIcon && <TrendIcon size={14} />}
                      <span class="font-medium">
                        {typeof stat.change.value === 'number' && stat.change.value > 0 && stat.change.type === 'increase' ? '+' : ''}
                        {formatValue(stat.change.value, stat.format === 'currency' ? 'currency' : 'number', stat.precision)}
                      </span>
                      {stat.change.period && (
                        <span class="text-white/50">
                          {stat.change.period}
                        </span>
                      )}
                    </div>
                  )}

                  {/* Description */}
                  {stat.description && (
                    <p class="text-white/50 text-xs mt-golden-xs">
                      {stat.description}
                    </p>
                  )}

                  {/* Mini Trend Chart */}
                  {stat.trend && renderMiniChart(stat.trend)}
                </div>

                {/* Icon */}
                {Icon && (
                  <div class={`w-12 h-12 rounded-lg flex items-center justify-center ml-golden-md ${
                    stat.color === 'cosmic' ? 'bg-cosmic-500/20' :
                    stat.color === 'golden' ? 'bg-golden-500/20' :
                    stat.color === 'divine' ? 'bg-divine-500/20' :
                    'bg-white/10'
                  }`}>
                    <Icon size={24} class={`${
                      stat.color === 'cosmic' ? 'text-cosmic-400' :
                      stat.color === 'golden' ? 'text-golden-400' :
                      stat.color === 'divine' ? 'text-divine-400' :
                      'text-white/70'
                    }`} />
                  </div>
                )}
              </div>
            </CosmicCard>
          )
        }}
      </For>
    </div>
  )
}

// Convenience component for single stat
export const StatCard: Component<{
  stat: StatItem
  size?: 'sm' | 'md' | 'lg'
  animated?: boolean
  class?: string
}> = (props) => {
  return (
    <StatsGrid
      stats={[props.stat]}
      columns={1}
      size={props.size}
      animated={props.animated}
      class={props.class}
    />
  )
}
