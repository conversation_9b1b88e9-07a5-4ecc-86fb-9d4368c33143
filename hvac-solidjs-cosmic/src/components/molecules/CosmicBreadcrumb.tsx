import { type Component, For } from 'solid-js'
import { A } from '@solidjs/router'
import { ChevronRight, Home } from 'lucide-solid'

export interface BreadcrumbItem {
  label: string
  href?: string
  icon?: Component<{ size: number; class?: string }>
  current?: boolean
}

export interface CosmicBreadcrumbProps {
  items: BreadcrumbItem[]
  showHome?: boolean
  separator?: Component<{ size: number; class?: string }>
  class?: string
}

export const CosmicBreadcrumb: Component<CosmicBreadcrumbProps> = (props) => {
  const Separator = props.separator || ChevronRight

  const allItems = () => {
    const items = [...props.items]
    
    if (props.showHome !== false && items[0]?.href !== '/') {
      items.unshift({
        label: 'Home',
        href: '/',
        icon: Home
      })
    }
    
    return items
  }

  return (
    <nav class={`flex items-center space-x-golden-sm text-sm ${props.class || ''}`} aria-label="Breadcrumb">
      <ol class="flex items-center space-x-golden-sm">
        <For each={allItems()}>
          {(item, index) => {
            const isLast = index() === allItems().length - 1
            const Icon = item.icon

            return (
              <li class="flex items-center space-x-golden-sm">
                {index() > 0 && (
                  <Separator size={16} class="text-white/40" />
                )}
                
                <div class="flex items-center space-x-golden-xs">
                  {Icon && (
                    <Icon size={16} class={isLast || item.current ? 'text-white' : 'text-white/60'} />
                  )}
                  
                  {item.href && !isLast && !item.current ? (
                    <A
                      href={item.href}
                      class="text-white/60 hover:text-white transition-colors duration-200 hover:underline"
                    >
                      {item.label}
                    </A>
                  ) : (
                    <span class={`font-medium ${isLast || item.current ? 'text-white' : 'text-white/60'}`}>
                      {item.label}
                    </span>
                  )}
                </div>
              </li>
            )
          }}
        </For>
      </ol>
    </nav>
  )
}

// Convenience hook for generating breadcrumbs from route
export const useBreadcrumbs = (pathname: string, customLabels?: Record<string, string>) => {
  const segments = pathname.split('/').filter(Boolean)
  
  const items: BreadcrumbItem[] = segments.map((segment, index) => {
    const href = '/' + segments.slice(0, index + 1).join('/')
    const label = customLabels?.[segment] || segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ')
    
    return {
      label,
      href,
      current: index === segments.length - 1
    }
  })

  return items
}
