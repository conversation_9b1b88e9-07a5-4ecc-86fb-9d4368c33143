import { type Component, type JSX, For, createSignal, splitProps } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { AlertCircle, Check, Eye, EyeOff } from 'lucide-solid'

export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea' | 'select' | 'checkbox' | 'radio'
  placeholder?: string
  required?: boolean
  disabled?: boolean
  options?: { value: string; label: string }[]
  validation?: (value: any) => string | null
  description?: string
  icon?: Component<{ size: number; class?: string }>
}

export interface CosmicFormProps {
  fields: FormField[]
  onSubmit: (data: Record<string, any>) => void | Promise<void>
  submitLabel?: string
  cancelLabel?: string
  onCancel?: () => void
  loading?: boolean
  variant?: 'glass' | 'cosmic' | 'golden' | 'divine'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  class?: string
  title?: string
  description?: string
}

export const CosmicForm: Component<CosmicFormProps> = (props) => {
  const [formData, setFormData] = createSignal<Record<string, any>>({})
  const [errors, setErrors] = createSignal<Record<string, string>>({})
  const [showPasswords, setShowPasswords] = createSignal<Record<string, boolean>>({})
  const [isSubmitting, setIsSubmitting] = createSignal(false)

  const updateField = (name: string, value: any) => {
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear error when user starts typing
    if (errors()[name]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  const validateField = (field: FormField, value: any): string | null => {
    if (field.required && (!value || value === '')) {
      return `${field.label} is required`
    }
    
    if (field.validation) {
      return field.validation(value)
    }
    
    return null
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    
    props.fields.forEach(field => {
      const value = formData()[field.name]
      const error = validateField(field, value)
      if (error) {
        newErrors[field.name] = error
      }
    })
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: Event) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setIsSubmitting(true)
    try {
      await props.onSubmit(formData())
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const togglePasswordVisibility = (fieldName: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [fieldName]: !prev[fieldName]
    }))
  }

  const renderField = (field: FormField) => {
    const value = formData()[field.name] || ''
    const error = errors()[field.name]
    const Icon = field.icon

    const baseInputClasses = `
      w-full bg-white/10 backdrop-blur-lg border rounded-lg px-golden-md py-golden-sm 
      text-white placeholder-white/50 transition-all duration-300
      focus:outline-none focus:ring-2 focus:ring-cosmic-400 focus:border-transparent
      ${error ? 'border-red-400' : 'border-white/20 hover:border-white/30'}
      ${field.disabled ? 'opacity-50 cursor-not-allowed' : ''}
    `.trim().replace(/\s+/g, ' ')

    return (
      <div class="space-y-golden-xs">
        <label class="block text-white/90 font-medium">
          {field.label}
          {field.required && <span class="text-red-400 ml-1">*</span>}
        </label>
        
        {field.description && (
          <p class="text-white/60 text-sm">{field.description}</p>
        )}

        <div class="relative">
          {Icon && (
            <Icon size={20} class="absolute left-golden-sm top-1/2 transform -translate-y-1/2 text-white/50" />
          )}
          
          {field.type === 'textarea' ? (
            <textarea
              class={`${baseInputClasses} ${Icon ? 'pl-10' : ''} min-h-[100px] resize-y`}
              placeholder={field.placeholder}
              value={value}
              disabled={field.disabled}
              onInput={(e) => updateField(field.name, e.target.value)}
            />
          ) : field.type === 'select' ? (
            <select
              class={`${baseInputClasses} ${Icon ? 'pl-10' : ''}`}
              value={value}
              disabled={field.disabled}
              onChange={(e) => updateField(field.name, e.target.value)}
            >
              <option value="">{field.placeholder || `Select ${field.label}`}</option>
              <For each={field.options}>
                {(option) => (
                  <option value={option.value} class="bg-gray-800 text-white">
                    {option.label}
                  </option>
                )}
              </For>
            </select>
          ) : field.type === 'checkbox' ? (
            <label class="flex items-center space-x-golden-sm cursor-pointer">
              <input
                type="checkbox"
                class="w-4 h-4 text-cosmic-400 bg-white/10 border-white/20 rounded focus:ring-cosmic-400 focus:ring-2"
                checked={value}
                disabled={field.disabled}
                onChange={(e) => updateField(field.name, e.target.checked)}
              />
              <span class="text-white/90">{field.placeholder || field.label}</span>
            </label>
          ) : field.type === 'radio' ? (
            <div class="space-y-golden-xs">
              <For each={field.options}>
                {(option) => (
                  <label class="flex items-center space-x-golden-sm cursor-pointer">
                    <input
                      type="radio"
                      name={field.name}
                      value={option.value}
                      class="w-4 h-4 text-cosmic-400 bg-white/10 border-white/20 focus:ring-cosmic-400 focus:ring-2"
                      checked={value === option.value}
                      disabled={field.disabled}
                      onChange={(e) => updateField(field.name, e.target.value)}
                    />
                    <span class="text-white/90">{option.label}</span>
                  </label>
                )}
              </For>
            </div>
          ) : (
            <input
              type={field.type === 'password' && showPasswords()[field.name] ? 'text' : field.type}
              class={`${baseInputClasses} ${Icon ? 'pl-10' : ''} ${field.type === 'password' ? 'pr-10' : ''}`}
              placeholder={field.placeholder}
              value={value}
              disabled={field.disabled}
              onInput={(e) => updateField(field.name, e.target.value)}
            />
          )}

          {field.type === 'password' && (
            <button
              type="button"
              class="absolute right-golden-sm top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white transition-colors"
              onClick={() => togglePasswordVisibility(field.name)}
            >
              {showPasswords()[field.name] ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          )}
        </div>

        {error && (
          <div class="flex items-center space-x-golden-xs text-red-400 text-sm">
            <AlertCircle size={16} />
            <span>{error}</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <CosmicCard variant={props.variant || 'glass'} size={props.size || 'lg'} glow class={props.class}>
      {(props.title || props.description) && (
        <div class="mb-golden-lg">
          {props.title && (
            <h2 class="text-2xl font-bold text-white mb-golden-sm">{props.title}</h2>
          )}
          {props.description && (
            <p class="text-white/70">{props.description}</p>
          )}
        </div>
      )}

      <form onSubmit={handleSubmit} class="space-y-golden-md">
        <For each={props.fields}>
          {(field) => renderField(field)}
        </For>

        <div class="flex items-center space-x-golden-md pt-golden-md">
          <GoldenButton
            type="submit"
            variant="cosmic"
            size="lg"
            glow
            physics
            disabled={isSubmitting() || props.loading}
            class="flex-1"
          >
            {isSubmitting() || props.loading ? (
              <div class="flex items-center space-x-golden-sm">
                <div class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                <span>Processing...</span>
              </div>
            ) : (
              <div class="flex items-center space-x-golden-sm">
                <Check size={20} />
                <span>{props.submitLabel || 'Submit'}</span>
              </div>
            )}
          </GoldenButton>

          {props.onCancel && (
            <GoldenButton
              type="button"
              variant="glass"
              size="lg"
              onClick={props.onCancel}
              disabled={isSubmitting() || props.loading}
            >
              {props.cancelLabel || 'Cancel'}
            </GoldenButton>
          )}
        </div>
      </form>
    </CosmicCard>
  )
}
