import { type Component, type JSX, For, createSignal } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { ChevronLeft, ChevronRight, Search, Filter, Download } from 'lucide-solid'

export interface Column<T> {
  key: keyof T
  label: string
  sortable?: boolean
  render?: (value: any, row: T) => JSX.Element
  width?: string
}

export interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  searchable?: boolean
  filterable?: boolean
  exportable?: boolean
  pageSize?: number
  loading?: boolean
  onRowClick?: (row: T) => void
  onSearch?: (term: string) => void
  onFilter?: (filters: Record<string, any>) => void
  onExport?: () => void
  class?: string
}

export const DataTable = <T extends Record<string, any>>(props: DataTableProps<T>): JSX.Element => {
  const [currentPage, setCurrentPage] = createSignal(1)
  const [sortColumn, setSortColumn] = createSignal<keyof T | null>(null)
  const [sortDirection, setSortDirection] = createSignal<'asc' | 'desc'>('asc')
  const [searchTerm, setSearchTerm] = createSignal('')

  const pageSize = () => props.pageSize || 10
  const totalPages = () => Math.ceil(props.data.length / pageSize())

  const handleSort = (column: keyof T) => {
    if (!props.columns.find(col => col.key === column)?.sortable) return

    if (sortColumn() === column) {
      setSortDirection(sortDirection() === 'asc' ? 'desc' : 'asc')
    } else {
      setSortColumn(column)
      setSortDirection('asc')
    }
  }

  const sortedData = () => {
    let data = [...props.data]
    
    if (sortColumn()) {
      data.sort((a, b) => {
        const aVal = a[sortColumn()!]
        const bVal = b[sortColumn()!]
        
        if (aVal < bVal) return sortDirection() === 'asc' ? -1 : 1
        if (aVal > bVal) return sortDirection() === 'asc' ? 1 : -1
        return 0
      })
    }

    return data
  }

  const paginatedData = () => {
    const start = (currentPage() - 1) * pageSize()
    const end = start + pageSize()
    return sortedData().slice(start, end)
  }

  const handleSearch = (term: string) => {
    setSearchTerm(term)
    setCurrentPage(1)
    props.onSearch?.(term)
  }

  return (
    <CosmicCard variant="glass" size="lg" glow class={props.class}>
      {/* Header with Search and Actions */}
      {(props.searchable || props.filterable || props.exportable) && (
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-golden-md space-y-golden-sm lg:space-y-0">
          {/* Search */}
          {props.searchable && (
            <div class="relative flex-1 max-w-md">
              <Search size={20} class="absolute left-golden-sm top-1/2 transform -translate-y-1/2 text-white/50" />
              <input
                type="text"
                placeholder="Search..."
                class="w-full bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg pl-10 pr-golden-md py-golden-sm text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                value={searchTerm()}
                onInput={(e) => handleSearch(e.target.value)}
              />
            </div>
          )}

          {/* Actions */}
          <div class="flex items-center space-x-golden-sm">
            {props.filterable && (
              <GoldenButton variant="cosmic" size="sm" glow>
                <Filter size={16} class="mr-golden-xs" />
                Filter
              </GoldenButton>
            )}
            {props.exportable && (
              <GoldenButton variant="golden" size="sm" glow onClick={props.onExport}>
                <Download size={16} class="mr-golden-xs" />
                Export
              </GoldenButton>
            )}
          </div>
        </div>
      )}

      {/* Table */}
      <div class="overflow-x-auto">
        <table class="w-full">
          {/* Header */}
          <thead>
            <tr class="border-b border-white/20">
              <For each={props.columns}>
                {(column) => (
                  <th
                    class={`text-left py-golden-sm px-golden-sm text-white/80 font-medium ${
                      column.sortable ? 'cursor-pointer hover:text-white transition-colors' : ''
                    }`}
                    style={{ width: column.width }}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div class="flex items-center space-x-golden-xs">
                      <span>{column.label}</span>
                      {column.sortable && sortColumn() === column.key && (
                        <span class="text-cosmic-400">
                          {sortDirection() === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                )}
              </For>
            </tr>
          </thead>

          {/* Body */}
          <tbody>
            {props.loading ? (
              <tr>
                <td colSpan={props.columns.length} class="text-center py-golden-xl">
                  <div class="text-white/50">Loading...</div>
                </td>
              </tr>
            ) : paginatedData().length === 0 ? (
              <tr>
                <td colSpan={props.columns.length} class="text-center py-golden-xl">
                  <div class="text-white/50">No data available</div>
                </td>
              </tr>
            ) : (
              <For each={paginatedData()}>
                {(row) => (
                  <tr
                    class={`border-b border-white/10 hover:bg-white/5 transition-colors ${
                      props.onRowClick ? 'cursor-pointer' : ''
                    }`}
                    onClick={() => props.onRowClick?.(row)}
                  >
                    <For each={props.columns}>
                      {(column) => (
                        <td class="py-golden-sm px-golden-sm text-white/90">
                          {column.render ? column.render(row[column.key], row) : String(row[column.key])}
                        </td>
                      )}
                    </For>
                  </tr>
                )}
              </For>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages() > 1 && (
        <div class="flex items-center justify-between mt-golden-md pt-golden-md border-t border-white/20">
          <div class="text-white/70 text-sm">
            Showing {((currentPage() - 1) * pageSize()) + 1} to {Math.min(currentPage() * pageSize(), props.data.length)} of {props.data.length} entries
          </div>
          
          <div class="flex items-center space-x-golden-sm">
            <GoldenButton
              variant="glass"
              size="sm"
              disabled={currentPage() === 1}
              onClick={() => setCurrentPage(Math.max(1, currentPage() - 1))}
            >
              <ChevronLeft size={16} />
            </GoldenButton>
            
            <span class="text-white/80 text-sm px-golden-sm">
              Page {currentPage()} of {totalPages()}
            </span>
            
            <GoldenButton
              variant="glass"
              size="sm"
              disabled={currentPage() === totalPages()}
              onClick={() => setCurrentPage(Math.min(totalPages(), currentPage() + 1))}
            >
              <ChevronRight size={16} />
            </GoldenButton>
          </div>
        </div>
      )}
    </CosmicCard>
  )
}
