import { type Component, type JSX, createSignal, onMount, onCleanup, Show } from 'solid-js'
import { Portal } from 'solid-js/web'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { X } from 'lucide-solid'

export interface CosmicModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  description?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  variant?: 'glass' | 'cosmic' | 'golden' | 'divine'
  closable?: boolean
  closeOnOverlayClick?: boolean
  closeOnEscape?: boolean
  showCloseButton?: boolean
  children: JSX.Element
  footer?: JSX.Element
  class?: string
}

export const CosmicModal: Component<CosmicModalProps> = (props) => {
  const [isAnimating, setIsAnimating] = createSignal(false)
  const [shouldRender, setShouldRender] = createSignal(false)

  // Handle modal open/close animations
  onMount(() => {
    if (props.isOpen) {
      setShouldRender(true)
      setTimeout(() => setIsAnimating(true), 10)
    }
  })

  // Watch for isOpen changes
  const handleOpenChange = () => {
    if (props.isOpen) {
      setShouldRender(true)
      setTimeout(() => setIsAnimating(true), 10)
    } else {
      setIsAnimating(false)
      setTimeout(() => setShouldRender(false), 300)
    }
  }

  // Handle escape key
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && props.closeOnEscape !== false && props.closable !== false) {
      props.onClose()
    }
  }

  onMount(() => {
    document.addEventListener('keydown', handleKeyDown)
    onCleanup(() => {
      document.removeEventListener('keydown', handleKeyDown)
    })
  })

  // Update animation state when isOpen changes
  $: if (props.isOpen !== isAnimating()) {
    handleOpenChange()
  }

  const getSizeClasses = () => {
    switch (props.size) {
      case 'sm':
        return 'max-w-md'
      case 'md':
        return 'max-w-lg'
      case 'lg':
        return 'max-w-2xl'
      case 'xl':
        return 'max-w-4xl'
      case 'full':
        return 'max-w-full mx-golden-md'
      default:
        return 'max-w-lg'
    }
  }

  const handleOverlayClick = (e: MouseEvent) => {
    if (e.target === e.currentTarget && props.closeOnOverlayClick !== false && props.closable !== false) {
      props.onClose()
    }
  }

  return (
    <Show when={shouldRender()}>
      <Portal>
        <div
          class={`fixed inset-0 z-50 flex items-center justify-center p-golden-md transition-all duration-300 ${
            isAnimating() ? 'opacity-100' : 'opacity-0'
          }`}
          onClick={handleOverlayClick}
        >
          {/* Backdrop */}
          <div class="absolute inset-0 bg-black/50 backdrop-blur-sm" />

          {/* Cosmic Background Effects */}
          <div class="absolute inset-0 pointer-events-none">
            {/* Floating Orbs */}
            {Array.from({ length: 8 }, (_, index) => (
              <div
                class="absolute rounded-full bg-white/5 backdrop-blur-sm animate-pulse"
                style={{
                  width: `${Math.random() * 60 + 20}px`,
                  height: `${Math.random() * 60 + 20}px`,
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  'animation-delay': `${index * 0.4}s`,
                  'animation-duration': `${5 + Math.random() * 3}s`
                }}
              />
            ))}

            {/* Golden Ratio Spiral */}
            <div class="absolute top-1/3 right-1/4 transform opacity-5">
              <svg width="200" height="200" viewBox="0 0 200 200">
                <path
                  d="M100,100 Q150,100 150,50 Q150,0 100,0 Q50,0 50,50 Q50,100 100,100"
                  fill="none"
                  stroke="white"
                  stroke-width="1"
                  class="animate-pulse"
                />
              </svg>
            </div>
          </div>

          {/* Modal Content */}
          <div
            class={`relative w-full ${getSizeClasses()} transform transition-all duration-300 ${
              isAnimating() ? 'scale-100 translate-y-0' : 'scale-95 translate-y-4'
            }`}
          >
            <CosmicCard 
              variant={props.variant || 'glass'} 
              size="lg" 
              glow 
              class={`relative ${props.class || ''}`}
            >
              {/* Header */}
              {(props.title || props.description || (props.showCloseButton !== false && props.closable !== false)) && (
                <div class="flex items-start justify-between mb-golden-md">
                  <div class="flex-1">
                    {props.title && (
                      <h2 class="text-2xl font-bold text-white mb-golden-xs">
                        {props.title}
                      </h2>
                    )}
                    {props.description && (
                      <p class="text-white/70">
                        {props.description}
                      </p>
                    )}
                  </div>

                  {props.showCloseButton !== false && props.closable !== false && (
                    <button
                      class="ml-golden-md text-white/60 hover:text-white transition-colors p-golden-xs rounded-lg hover:bg-white/10"
                      onClick={props.onClose}
                    >
                      <X size={24} />
                    </button>
                  )}
                </div>
              )}

              {/* Content */}
              <div class="relative">
                {props.children}
              </div>

              {/* Footer */}
              {props.footer && (
                <div class="mt-golden-md pt-golden-md border-t border-white/20">
                  {props.footer}
                </div>
              )}
            </CosmicCard>
          </div>
        </div>
      </Portal>
    </Show>
  )
}

// Convenience components for common modal types
export const ConfirmModal: Component<{
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  message: string
  confirmLabel?: string
  cancelLabel?: string
  variant?: 'cosmic' | 'golden' | 'divine'
  destructive?: boolean
}> = (props) => {
  return (
    <CosmicModal
      isOpen={props.isOpen}
      onClose={props.onClose}
      title={props.title}
      size="sm"
      variant={props.variant || 'glass'}
      footer={
        <div class="flex items-center space-x-golden-md">
          <GoldenButton
            variant={props.destructive ? 'divine' : 'cosmic'}
            size="md"
            glow
            onClick={props.onConfirm}
            class="flex-1"
          >
            {props.confirmLabel || 'Confirm'}
          </GoldenButton>
          <GoldenButton
            variant="glass"
            size="md"
            onClick={props.onClose}
            class="flex-1"
          >
            {props.cancelLabel || 'Cancel'}
          </GoldenButton>
        </div>
      }
    >
      <p class="text-white/80 leading-relaxed">
        {props.message}
      </p>
    </CosmicModal>
  )
}

export const AlertModal: Component<{
  isOpen: boolean
  onClose: () => void
  title: string
  message: string
  type?: 'info' | 'success' | 'warning' | 'error'
}> = (props) => {
  const getVariant = () => {
    switch (props.type) {
      case 'success':
        return 'cosmic'
      case 'warning':
        return 'golden'
      case 'error':
        return 'divine'
      default:
        return 'glass'
    }
  }

  return (
    <CosmicModal
      isOpen={props.isOpen}
      onClose={props.onClose}
      title={props.title}
      size="sm"
      variant={getVariant()}
      footer={
        <GoldenButton
          variant="cosmic"
          size="md"
          glow
          onClick={props.onClose}
          class="w-full"
        >
          OK
        </GoldenButton>
      }
    >
      <p class="text-white/80 leading-relaxed">
        {props.message}
      </p>
    </CosmicModal>
  )
}
