import { type Component, createSignal, onMount, onCleanup, Show } from 'solid-js'
import { Portal } from 'solid-js/web'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { CheckCircle, AlertCircle, Info, AlertTriangle, X } from 'lucide-solid'

export interface ToastProps {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  persistent?: boolean
  action?: {
    label: string
    onClick: () => void
  }
  onClose: (id: string) => void
}

export const CosmicToast: Component<ToastProps> = (props) => {
  const [isVisible, setIsVisible] = createSignal(false)
  const [isExiting, setIsExiting] = createSignal(false)

  let timeoutId: number | undefined

  onMount(() => {
    // Animate in
    setTimeout(() => setIsVisible(true), 10)

    // Auto-close if not persistent
    if (!props.persistent && props.duration !== 0) {
      timeoutId = setTimeout(() => {
        handleClose()
      }, props.duration || 5000)
    }
  })

  onCleanup(() => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
  })

  const handleClose = () => {
    setIsExiting(true)
    setTimeout(() => {
      props.onClose(props.id)
    }, 300)
  }

  const getIcon = () => {
    switch (props.type) {
      case 'success':
        return CheckCircle
      case 'error':
        return AlertCircle
      case 'warning':
        return AlertTriangle
      case 'info':
        return Info
    }
  }

  const getVariant = () => {
    switch (props.type) {
      case 'success':
        return 'cosmic'
      case 'error':
        return 'divine'
      case 'warning':
        return 'golden'
      case 'info':
        return 'glass'
    }
  }

  const getIconColor = () => {
    switch (props.type) {
      case 'success':
        return 'text-green-400'
      case 'error':
        return 'text-red-400'
      case 'warning':
        return 'text-yellow-400'
      case 'info':
        return 'text-blue-400'
    }
  }

  const Icon = getIcon()

  return (
    <div
      class={`transform transition-all duration-300 ease-out ${
        isVisible() && !isExiting()
          ? 'translate-x-0 opacity-100 scale-100'
          : 'translate-x-full opacity-0 scale-95'
      }`}
    >
      <CosmicCard variant={getVariant()} size="md" glow class="min-w-[320px] max-w-md">
        <div class="flex items-start space-x-golden-md">
          {/* Icon */}
          <div class="flex-shrink-0">
            <Icon size={24} class={getIconColor()} />
          </div>

          {/* Content */}
          <div class="flex-1 min-w-0">
            <h4 class="text-white font-semibold text-sm mb-golden-xs">
              {props.title}
            </h4>
            {props.message && (
              <p class="text-white/80 text-sm leading-relaxed">
                {props.message}
              </p>
            )}
            
            {/* Action Button */}
            {props.action && (
              <div class="mt-golden-sm">
                <GoldenButton
                  variant="glass"
                  size="sm"
                  onClick={props.action.onClick}
                >
                  {props.action.label}
                </GoldenButton>
              </div>
            )}
          </div>

          {/* Close Button */}
          <button
            class="flex-shrink-0 text-white/60 hover:text-white transition-colors p-1 rounded hover:bg-white/10"
            onClick={handleClose}
          >
            <X size={16} />
          </button>
        </div>
      </CosmicCard>
    </div>
  )
}

// Toast Container Component
export const ToastContainer: Component<{
  toasts: ToastProps[]
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
}> = (props) => {
  const getPositionClasses = () => {
    switch (props.position || 'top-right') {
      case 'top-right':
        return 'top-golden-lg right-golden-lg'
      case 'top-left':
        return 'top-golden-lg left-golden-lg'
      case 'bottom-right':
        return 'bottom-golden-lg right-golden-lg'
      case 'bottom-left':
        return 'bottom-golden-lg left-golden-lg'
      case 'top-center':
        return 'top-golden-lg left-1/2 transform -translate-x-1/2'
      case 'bottom-center':
        return 'bottom-golden-lg left-1/2 transform -translate-x-1/2'
    }
  }

  return (
    <Show when={props.toasts.length > 0}>
      <Portal>
        <div class={`fixed z-50 ${getPositionClasses()}`}>
          <div class="space-y-golden-sm">
            {props.toasts.map((toast) => (
              <CosmicToast {...toast} />
            ))}
          </div>
        </div>
      </Portal>
    </Show>
  )
}

// Toast Store for managing toasts globally
class ToastStore {
  private toasts = createSignal<ToastProps[]>([])
  private idCounter = 0

  getToasts() {
    return this.toasts[0]()
  }

  addToast(toast: Omit<ToastProps, 'id' | 'onClose'>) {
    const id = `toast-${++this.idCounter}`
    const newToast: ToastProps = {
      ...toast,
      id,
      onClose: (id) => this.removeToast(id)
    }

    this.toasts[1](prev => [...prev, newToast])
    return id
  }

  removeToast(id: string) {
    this.toasts[1](prev => prev.filter(toast => toast.id !== id))
  }

  clearAll() {
    this.toasts[1]([])
  }

  // Convenience methods
  success(title: string, message?: string, options?: Partial<ToastProps>) {
    return this.addToast({ type: 'success', title, message, ...options })
  }

  error(title: string, message?: string, options?: Partial<ToastProps>) {
    return this.addToast({ type: 'error', title, message, ...options })
  }

  warning(title: string, message?: string, options?: Partial<ToastProps>) {
    return this.addToast({ type: 'warning', title, message, ...options })
  }

  info(title: string, message?: string, options?: Partial<ToastProps>) {
    return this.addToast({ type: 'info', title, message, ...options })
  }
}

// Global toast instance
export const toast = new ToastStore()

// Hook for using toasts in components
export const useToast = () => {
  return {
    toasts: toast.getToasts(),
    addToast: toast.addToast.bind(toast),
    removeToast: toast.removeToast.bind(toast),
    clearAll: toast.clearAll.bind(toast),
    success: toast.success.bind(toast),
    error: toast.error.bind(toast),
    warning: toast.warning.bind(toast),
    info: toast.info.bind(toast)
  }
}
