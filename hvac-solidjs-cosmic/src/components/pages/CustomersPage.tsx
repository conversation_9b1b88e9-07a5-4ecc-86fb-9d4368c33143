import { type Component, createSignal, onMount, For, Show, createMemo, createEffect } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import {
  Users,
  Search,
  Plus,
  Mail,
  Phone,
  MapPin,
  Edit,
  Trash2,
  Eye,
  Filter,
  Download,
  Building,
  Tag,
  Activity,
  MoreVertical,
  UserPlus,
  Upload,
  Zap,
  Brain,
  Target,
  Star,
  TrendingUp,
  Calendar,
  RefreshCw
} from 'lucide-solid'
import { useCustomers, useCreateCustomer, useDeleteCustomer, apiUtils } from '../../lib/api'
import type { Customer, SearchFilters, PaginationParams } from '../../lib/api'
import { toast } from '../molecules/CosmicToast'

export const CustomersPage: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [searchTerm, setSearchTerm] = createSignal('')
  const [selectedStatus, setSelectedStatus] = createSignal('all')
  const [showAddModal, setShowAddModal] = createSignal(false)
  const [currentPage, setCurrentPage] = createSignal(1)
  const [pageSize, setPageSize] = createSignal(20)
  const [sortBy, setSortBy] = createSignal('name')
  const [sortOrder, setSortOrder] = createSignal<'asc' | 'desc'>('asc')

  // 🌟 Real API Integration
  const filters = createMemo((): SearchFilters =>
    apiUtils.createFilters({
      query: searchTerm(),
      status: selectedStatus() === 'all' ? undefined : [selectedStatus()]
    })
  )

  const pagination = createMemo((): PaginationParams =>
    apiUtils.createPagination({
      page: currentPage(),
      limit: pageSize(),
      sortBy: sortBy(),
      sortOrder: sortOrder()
    })
  )

  const customersQuery = useCustomers(filters(), pagination())
  const createCustomerMutation = useCreateCustomer()
  const deleteCustomerMutation = useDeleteCustomer()

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 200)
  })

  // 🔄 Auto-refresh when filters change
  createEffect(() => {
    if (searchTerm() || selectedStatus()) {
      setCurrentPage(1) // Reset to first page when filtering
    }
  })

  // 🎯 Customer Actions
  const handleDeleteCustomer = async (customerId: string) => {
    if (confirm('Are you sure you want to delete this customer?')) {
      try {
        await deleteCustomerMutation.mutateAsync(customerId)
        toast.success('Customer deleted successfully!')
      } catch (error) {
        toast.error(apiUtils.formatError(error))
      }
    }
  }

  const handleCreateCustomer = async (customerData: any) => {
    try {
      await createCustomerMutation.mutateAsync(customerData)
      toast.success('Customer created successfully!')
      setShowAddModal(false)
    } catch (error) {
      toast.error(apiUtils.formatError(error))
    }
  }

  // 📊 Dynamic customer data from API or fallback
  const customers = () => customersQuery.data?.data || []
  const totalCustomers = () => customersQuery.data?.total || 0
  const totalPages = () => customersQuery.data?.totalPages || 1

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500'
      case 'pending':
        return 'bg-yellow-500'
      case 'inactive':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getStatusText = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1)
  }

  // 🧠 AI-Powered Customer Insights
  const getCustomerScore = (customer: Customer) => {
    // Simple scoring algorithm - in real app this would come from AI service
    const recencyScore = new Date(customer.updatedAt).getTime() > Date.now() - 30 * 24 * 60 * 60 * 1000 ? 30 : 10
    const statusScore = customer.status === 'active' ? 40 : customer.status === 'prospect' ? 20 : 10
    const engagementScore = Math.min((customer.tags?.length || 0) * 10, 30)
    return Math.min(recencyScore + statusScore + engagementScore, 100)
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400'
    if (score >= 60) return 'text-yellow-400'
    return 'text-red-400'
  }

  return (
    <div class="p-golden-lg space-y-golden-lg">
      {/* Header */}
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div class="flex items-center space-x-golden-md">
          <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-lg flex items-center justify-center">
            <Users size={24} class="text-white" />
          </div>
          <div>
            <h1 class="text-3xl font-bold text-white">Customers</h1>
            <p class="text-white/70">Manage your customer relationships</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-golden-sm mt-golden-md lg:mt-0">
          {/* Real-time Status */}
          <div class="flex items-center space-x-golden-xs">
            <Show
              when={!customersQuery.isLoading}
              fallback={
                <div class="flex items-center space-x-golden-xs text-yellow-400">
                  <RefreshCw size={14} class="animate-spin" />
                  <span class="text-xs">Loading...</span>
                </div>
              }
            >
              <div class="flex items-center space-x-golden-xs text-green-400">
                <Activity size={14} />
                <span class="text-xs">{totalCustomers()} customers</span>
              </div>
            </Show>
          </div>

          <GoldenButton
            variant="cosmic"
            size="md"
            glow
            onClick={() => customersQuery.refetch()}
            disabled={customersQuery.isFetching}
          >
            <RefreshCw size={16} class={`mr-golden-xs ${customersQuery.isFetching ? 'animate-spin' : ''}`} />
            Refresh
          </GoldenButton>

          <GoldenButton variant="divine" size="md" glow>
            <Download size={16} class="mr-golden-xs" />
            Export
          </GoldenButton>

          <GoldenButton variant="golden" size="md" glow onClick={() => setShowAddModal(true)}>
            <UserPlus size={16} class="mr-golden-xs" />
            Add Customer
          </GoldenButton>
        </div>
      </div>

      {/* Filters and Search */}
      <div
        class={`transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="md" glow>
          <div class="flex flex-col lg:flex-row lg:items-center space-y-golden-md lg:space-y-0 lg:space-x-golden-md">
            {/* Search */}
            <div class="flex-1 relative">
              <Search size={20} class="absolute left-golden-sm top-1/2 transform -translate-y-1/2 text-white/50" />
              <input
                type="text"
                placeholder="Search customers..."
                class="w-full bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg pl-10 pr-golden-md py-golden-sm text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                value={searchTerm()}
                onInput={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Status Filter */}
            <div class="flex items-center space-x-golden-sm">
              <Filter size={16} class="text-white/70" />
              <select
                class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg px-golden-md py-golden-sm text-white focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                value={selectedStatus()}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </CosmicCard>
      </div>

      {/* Enhanced Customer Stats with AI Insights */}
      <div
        class={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md transition-all duration-1000 delay-400 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="cosmic" size="md" glow hover3d>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-white mb-golden-xs">
                <Show
                  when={!customersQuery.isLoading}
                  fallback={<div class="h-8 bg-white/20 rounded animate-pulse"></div>}
                >
                  {customers().filter(c => c.status === 'active').length}
                </Show>
              </div>
              <div class="text-white/70 text-sm">Active Customers</div>
              <div class="flex items-center space-x-golden-xs mt-golden-xs">
                <TrendingUp size={12} class="text-green-400" />
                <span class="text-green-400 text-xs">+12.5%</span>
              </div>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-cosmic-600 rounded-lg flex items-center justify-center">
              <Users size={24} class="text-white" />
            </div>
          </div>
        </CosmicCard>

        <CosmicCard variant="golden" size="md" glow hover3d>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-white mb-golden-xs">
                <Show
                  when={!customersQuery.isLoading}
                  fallback={<div class="h-8 bg-white/20 rounded animate-pulse"></div>}
                >
                  {customers().filter(c => c.status === 'prospect').length}
                </Show>
              </div>
              <div class="text-white/70 text-sm">Prospects</div>
              <div class="flex items-center space-x-golden-xs mt-golden-xs">
                <Target size={12} class="text-yellow-400" />
                <span class="text-yellow-400 text-xs">Hot leads</span>
              </div>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-golden-400 to-golden-600 rounded-lg flex items-center justify-center">
              <Target size={24} class="text-white" />
            </div>
          </div>
        </CosmicCard>

        <CosmicCard variant="divine" size="md" glow hover3d>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-white mb-golden-xs">
                <Show
                  when={!customersQuery.isLoading}
                  fallback={<div class="h-8 bg-white/20 rounded animate-pulse"></div>}
                >
                  {Math.round(customers().reduce((sum, c) => sum + getCustomerScore(c), 0) / Math.max(customers().length, 1))}
                </Show>
              </div>
              <div class="text-white/70 text-sm">Avg AI Score</div>
              <div class="flex items-center space-x-golden-xs mt-golden-xs">
                <Brain size={12} class="text-purple-400" />
                <span class="text-purple-400 text-xs">AI Powered</span>
              </div>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-divine-400 to-divine-600 rounded-lg flex items-center justify-center">
              <Brain size={24} class="text-white" />
            </div>
          </div>
        </CosmicCard>

        <CosmicCard variant="glass" size="md" glow hover3d>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-white mb-golden-xs">
                <Show
                  when={!customersQuery.isLoading}
                  fallback={<div class="h-8 bg-white/20 rounded animate-pulse"></div>}
                >
                  {totalPages()}
                </Show>
              </div>
              <div class="text-white/70 text-sm">Total Pages</div>
              <div class="flex items-center space-x-golden-xs mt-golden-xs">
                <Calendar size={12} class="text-blue-400" />
                <span class="text-blue-400 text-xs">Page {currentPage()}</span>
              </div>
            </div>
            <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-600 rounded-lg flex items-center justify-center">
              <Activity size={24} class="text-white" />
            </div>
          </div>
        </CosmicCard>
      </div>

      {/* Enhanced Customer List with AI Insights */}
      <div
        class={`transition-all duration-1000 delay-600 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="lg" glow>
          <div class="flex items-center justify-between mb-golden-md">
            <h3 class="text-xl font-bold text-white">Customer Directory</h3>
            <div class="flex items-center space-x-golden-sm">
              <span class="text-white/70 text-sm">
                Showing {customers().length} of {totalCustomers()} customers
              </span>
              <select
                class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg px-golden-sm py-1 text-white text-sm focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                value={pageSize()}
                onChange={(e) => setPageSize(Number(e.target.value))}
              >
                <option value={10}>10 per page</option>
                <option value={20}>20 per page</option>
                <option value={50}>50 per page</option>
              </select>
            </div>
          </div>

          <Show
            when={!customersQuery.isLoading}
            fallback={
              <div class="space-y-golden-md">
                {Array.from({ length: 5 }, (_, index) => (
                  <div class="bg-white/5 rounded-lg p-golden-md border border-white/10 animate-pulse">
                    <div class="flex items-center justify-between">
                      <div class="flex-1">
                        <div class="h-6 bg-white/20 rounded mb-golden-xs w-1/3"></div>
                        <div class="h-4 bg-white/20 rounded mb-golden-sm w-1/4"></div>
                        <div class="grid grid-cols-3 gap-golden-sm">
                          <div class="h-4 bg-white/20 rounded"></div>
                          <div class="h-4 bg-white/20 rounded"></div>
                          <div class="h-4 bg-white/20 rounded"></div>
                        </div>
                      </div>
                      <div class="flex space-x-golden-xs">
                        <div class="w-8 h-8 bg-white/20 rounded"></div>
                        <div class="w-8 h-8 bg-white/20 rounded"></div>
                        <div class="w-8 h-8 bg-white/20 rounded"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            }
          >
            <div class="space-y-golden-md">
              <For each={customers()}>
                {(customer) => {
                  const aiScore = getCustomerScore(customer)
                  return (
                    <div class="bg-white/5 rounded-lg p-golden-md border border-white/10 hover:border-white/20 transition-all duration-300 hover:bg-white/10 group">
                      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-golden-sm lg:space-y-0">
                        {/* Customer Info with AI Score */}
                        <div class="flex-1">
                          <div class="flex items-center space-x-golden-md mb-golden-sm">
                            <div class="flex-1">
                              <div class="flex items-center space-x-golden-sm">
                                <h3 class="text-lg font-bold text-white">{customer.name}</h3>
                                <div class="flex items-center space-x-golden-xs">
                                  <Brain size={12} class="text-purple-400" />
                                  <span class={`text-xs font-bold ${getScoreColor(aiScore)}`}>
                                    {aiScore}
                                  </span>
                                </div>
                              </div>
                              <div class="flex items-center space-x-golden-sm">
                                <p class="text-white/70 text-sm">{customer.id}</p>
                                <div class={`w-2 h-2 rounded-full ${getStatusColor(customer.status)}`}></div>
                                <span class="text-white/70 text-xs">{getStatusText(customer.status)}</span>
                              </div>
                            </div>
                          </div>

                          <div class="grid grid-cols-1 md:grid-cols-3 gap-golden-sm text-sm">
                            <div class="flex items-center space-x-golden-xs text-white/70">
                              <Mail size={14} />
                              <span class="truncate">{customer.email}</span>
                            </div>
                            <div class="flex items-center space-x-golden-xs text-white/70">
                              <Phone size={14} />
                              <span>{customer.phone || 'No phone'}</span>
                            </div>
                            <div class="flex items-center space-x-golden-xs text-white/70">
                              <MapPin size={14} />
                              <span class="truncate">{customer.address || 'No address'}</span>
                            </div>
                          </div>

                          {/* Tags */}
                          <Show when={customer.tags && customer.tags.length > 0}>
                            <div class="flex items-center space-x-golden-xs mt-golden-sm">
                              <Tag size={12} class="text-white/50" />
                              <div class="flex flex-wrap gap-1">
                                <For each={customer.tags?.slice(0, 3)}>
                                  {(tag) => (
                                    <span class="px-2 py-0.5 bg-cosmic-500/20 text-cosmic-300 text-xs rounded-full">
                                      {tag}
                                    </span>
                                  )}
                                </For>
                                <Show when={(customer.tags?.length || 0) > 3}>
                                  <span class="text-white/50 text-xs">+{(customer.tags?.length || 0) - 3} more</span>
                                </Show>
                              </div>
                            </div>
                          </Show>
                        </div>

                        {/* Enhanced Actions */}
                        <div class="flex items-center space-x-golden-sm">
                          <GoldenButton
                            variant="cosmic"
                            size="sm"
                            glow
                            title="View Details"
                          >
                            <Eye size={14} />
                          </GoldenButton>
                          <GoldenButton
                            variant="golden"
                            size="sm"
                            glow
                            title="Edit Customer"
                          >
                            <Edit size={14} />
                          </GoldenButton>
                          <GoldenButton
                            variant="divine"
                            size="sm"
                            glow
                            onClick={() => handleDeleteCustomer(customer.id)}
                            disabled={deleteCustomerMutation.isPending}
                            title="Delete Customer"
                          >
                            <Trash2 size={14} />
                          </GoldenButton>
                          <GoldenButton
                            variant="glass"
                            size="sm"
                            glow
                            title="More Actions"
                          >
                            <MoreVertical size={14} />
                          </GoldenButton>
                        </div>
                      </div>
                    </div>
                  )
                }}
              </For>
            </div>
          </Show>

          {/* Pagination */}
          <Show when={totalPages() > 1}>
            <div class="flex items-center justify-between mt-golden-lg pt-golden-md border-t border-white/10">
              <div class="text-white/70 text-sm">
                Page {currentPage()} of {totalPages()}
              </div>
              <div class="flex items-center space-x-golden-sm">
                <GoldenButton
                  variant="cosmic"
                  size="sm"
                  glow
                  onClick={() => setCurrentPage(Math.max(1, currentPage() - 1))}
                  disabled={currentPage() === 1}
                >
                  Previous
                </GoldenButton>
                <GoldenButton
                  variant="cosmic"
                  size="sm"
                  glow
                  onClick={() => setCurrentPage(Math.min(totalPages(), currentPage() + 1))}
                  disabled={currentPage() === totalPages()}
                >
                  Next
                </GoldenButton>
              </div>
            </div>
          </Show>
        </CosmicCard>
      </div>
    </div>
  )
}
