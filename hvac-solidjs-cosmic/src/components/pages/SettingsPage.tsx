import { type Component, createSignal, onMount } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { Settings, User, Bell, Shield, Palette, Database, Zap, Save } from 'lucide-solid'

export const SettingsPage: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 200)
  })

  const settingsCategories = [
    {
      title: 'Profile Settings',
      description: 'Manage your account and personal information',
      icon: User,
      color: 'cosmic'
    },
    {
      title: 'Notifications',
      description: 'Configure alerts and notification preferences',
      icon: Bell,
      color: 'golden'
    },
    {
      title: 'Security',
      description: 'Password, 2FA, and security settings',
      icon: Shield,
      color: 'divine'
    },
    {
      title: 'Appearance',
      description: 'Customize theme and visual preferences',
      icon: Palette,
      color: 'cosmic'
    },
    {
      title: 'Data Management',
      description: 'Backup, export, and data retention settings',
      icon: Database,
      color: 'golden'
    },
    {
      title: 'Integrations',
      description: 'Connect with external services and APIs',
      icon: Zap,
      color: 'divine'
    }
  ]

  return (
    <div class="p-golden-lg space-y-golden-lg">
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div class="flex items-center space-x-golden-md">
          <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-lg flex items-center justify-center">
            <Settings size={24} class="text-white" />
          </div>
          <div>
            <h1 class="text-3xl font-bold text-white">Settings</h1>
            <p class="text-white/70">Configure your HVAC CRM system</p>
          </div>
        </div>
        
        <GoldenButton variant="golden" size="lg" glow physics>
          <Save size={20} class="mr-golden-sm" />
          Save Changes
        </GoldenButton>
      </div>

      <div
        class={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-golden-lg transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        {settingsCategories.map((category, index) => {
          const Icon = category.icon
          return (
            <CosmicCard 
              variant={category.color as any} 
              size="lg" 
              glow 
              hover3d 
              physics
              class="cursor-pointer group"
            >
              <div class="text-center">
                <div class={`w-16 h-16 bg-gradient-to-r ${
                  category.color === 'cosmic' ? 'from-cosmic-400 to-cosmic-600' :
                  category.color === 'golden' ? 'from-golden-400 to-golden-600' :
                  'from-divine-400 to-divine-600'
                } rounded-lg flex items-center justify-center mx-auto mb-golden-md group-hover:scale-110 transition-transform duration-300`}>
                  <Icon size={32} class="text-white" />
                </div>
                <h3 class="text-xl font-bold text-white mb-golden-sm group-hover:text-golden-300 transition-colors">
                  {category.title}
                </h3>
                <p class="text-white/70 text-sm leading-relaxed">
                  {category.description}
                </p>
              </div>
            </CosmicCard>
          )
        })}
      </div>

      <CosmicCard variant="glass" size="lg" glow>
        <div class="text-center py-golden-xl">
          <Settings size={64} class="text-white/30 mx-auto mb-golden-md" />
          <h3 class="text-2xl font-bold text-white mb-golden-sm">System Configuration</h3>
          <p class="text-white/70 mb-golden-lg max-w-2xl mx-auto">
            Fine-tune your HVAC CRM system with cosmic precision. Every setting is optimized using the golden ratio and 137 fundamental truths.
          </p>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-golden-md">
            <GoldenButton variant="cosmic" size="lg" glow physics>
              <User size={20} class="mr-golden-sm" />
              User Management
            </GoldenButton>
            <GoldenButton variant="golden" size="lg" glow physics>
              <Shield size={20} class="mr-golden-sm" />
              Security Center
            </GoldenButton>
            <GoldenButton variant="divine" size="lg" glow physics>
              <Database size={20} class="mr-golden-sm" />
              System Health
            </GoldenButton>
          </div>
        </div>
      </CosmicCard>
    </div>
  )
}
