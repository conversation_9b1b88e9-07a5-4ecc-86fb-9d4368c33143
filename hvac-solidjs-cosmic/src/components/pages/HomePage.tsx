import { type Component, createSignal, onMount } from 'solid-js'
import { A } from '@solidjs/router'
import { GoldenButton } from '../atoms/GoldenButton'
import { CosmicCard } from '../atoms/CosmicCard'
import { 
  BarChart3, 
  Users, 
  Wrench, 
  Package, 
  TrendingUp,
  Zap,
  Star,
  Sparkles
} from 'lucide-solid'

export const HomePage: Component = () => {
  const [count, setCount] = createSignal(0)
  const [isLoaded, setIsLoaded] = createSignal(false)

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 200)
  })

  const features = [
    {
      icon: BarChart3,
      title: 'Advanced Analytics',
      description: 'Real-time insights powered by 137 cosmic algorithms',
      href: '/analytics',
      color: 'cosmic'
    },
    {
      icon: Users,
      title: 'Customer Management',
      description: 'Manage customers with golden ratio precision',
      href: '/customers',
      color: 'golden'
    },
    {
      icon: Wrench,
      title: 'Service Orders',
      description: 'Streamlined service workflows with divine efficiency',
      href: '/service-orders',
      color: 'divine'
    },
    {
      icon: Package,
      title: 'Inventory Control',
      description: 'Smart inventory management with quantum accuracy',
      href: '/inventory',
      color: 'cosmic'
    }
  ]

  const stats = [
    { label: 'Active Customers', value: '1,337', icon: Users },
    { label: 'Service Orders', value: '618', icon: Wrench },
    { label: 'Inventory Items', value: '2,618', icon: Package },
    { label: 'Revenue Growth', value: '+137%', icon: TrendingUp }
  ]

  return (
    <div class="p-golden-lg space-y-golden-xl">
      {/* Hero Section */}
      <div
        class={`text-center transition-all duration-1000 delay-300 ${
          isLoaded() ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
        }`}
      >
        <h1 class="text-5xl md:text-7xl font-bold mb-golden-lg">
          <span class="bg-gradient-to-r from-white via-golden-300 to-cosmic-300 bg-clip-text text-transparent">
            HVAC
          </span>
          <br />
          <span class="bg-gradient-to-r from-divine-300 via-cosmic-300 to-golden-300 bg-clip-text text-transparent">
            Cosmic CRM
          </span>
        </h1>
        <p class="text-xl md:text-2xl text-white/80 max-w-3xl mx-auto leading-relaxed mb-golden-lg">
          Experience the divine harmony of{' '}
          <span class="text-golden-300 font-semibold">Golden Ratio</span> design with{' '}
          <span class="text-cosmic-300 font-semibold">137 cosmic libraries</span> and{' '}
          <span class="text-divine-300 font-semibold">infinite possibilities</span>
        </p>
        
        <div class="flex flex-col sm:flex-row gap-golden-md justify-center items-center">
          <A href="/dashboard">
            <GoldenButton variant="cosmic" size="xl" glow physics>
              <Zap size={20} class="mr-golden-sm" />
              Launch Dashboard
            </GoldenButton>
          </A>
          <A href="/customers">
            <GoldenButton variant="golden" size="xl" glow physics>
              <Star size={20} class="mr-golden-sm" />
              Manage Customers
            </GoldenButton>
          </A>
        </div>
      </div>

      {/* Stats Section */}
      <div
        class={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md transition-all duration-1000 delay-500 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <CosmicCard variant="glass" size="md" glow hover3d>
              <div class="text-center">
                <div class="flex justify-center mb-golden-sm">
                  <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-full flex items-center justify-center">
                    <Icon size={24} class="text-white" />
                  </div>
                </div>
                <div class="text-2xl font-bold text-white mb-golden-xs">{stat.value}</div>
                <div class="text-white/70 text-sm">{stat.label}</div>
              </div>
            </CosmicCard>
          )
        })}
      </div>

      {/* Features Section */}
      <div
        class={`transition-all duration-1000 delay-700 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <h2 class="text-3xl font-bold text-white text-center mb-golden-lg">
          Cosmic Features
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-golden-lg">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <A href={feature.href}>
                <CosmicCard 
                  variant={feature.color as any} 
                  size="lg" 
                  glow 
                  hover3d 
                  physics
                  class="h-full cursor-pointer group"
                >
                  <div class="flex items-start space-x-golden-md">
                    <div class={`w-12 h-12 bg-gradient-to-r ${
                      feature.color === 'cosmic' ? 'from-cosmic-400 to-cosmic-600' :
                      feature.color === 'golden' ? 'from-golden-400 to-golden-600' :
                      'from-divine-400 to-divine-600'
                    } rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <Icon size={24} class="text-white" />
                    </div>
                    <div class="flex-1">
                      <h3 class="text-xl font-bold text-white mb-golden-sm group-hover:text-golden-300 transition-colors">
                        {feature.title}
                      </h3>
                      <p class="text-white/70 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </CosmicCard>
              </A>
            )
          })}
        </div>
      </div>

      {/* Interactive Demo Section */}
      <div
        class={`text-center transition-all duration-1000 delay-900 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="lg" glow class="max-w-md mx-auto">
          <div class="text-center">
            <h3 class="text-2xl font-bold mb-golden-md text-white flex items-center justify-center">
              <Sparkles size={24} class="mr-golden-sm text-golden-300" />
              Cosmic Counter
            </h3>
            <div class="text-4xl font-bold text-golden-300 mb-golden-md">
              {count()}
            </div>
            <div class="flex gap-golden-sm justify-center">
              <GoldenButton
                variant="cosmic"
                size="lg"
                glow
                physics
                onClick={() => setCount(count() + 1)}
              >
                Increment ✨
              </GoldenButton>
              <GoldenButton
                variant="divine"
                size="lg"
                glow
                physics
                onClick={() => setCount(0)}
              >
                Reset 🌟
              </GoldenButton>
            </div>
            <p class="text-white/60 text-sm mt-golden-md">
              Experience the power of SolidJS reactivity
            </p>
          </div>
        </CosmicCard>
      </div>

      {/* Footer */}
      <div
        class={`text-center text-white/60 transition-opacity duration-1000 delay-1200 ${
          isLoaded() ? 'opacity-100' : 'opacity-0'
        }`}
      >
        <p class="text-sm">
          Crafted with 💫 using SolidJS, Golden Ratio, and cosmic energy
        </p>
        <p class="text-xs mt-golden-xs">
          Powered by 137 fundamental truths of the universe
        </p>
      </div>
    </div>
  )
}
