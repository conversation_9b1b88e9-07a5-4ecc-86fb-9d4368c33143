import { type Component, createSignal, onMount } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { Package, Plus, Search, AlertTriangle, TrendingDown, TrendingUp } from 'lucide-solid'

export const InventoryPage: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 200)
  })

  return (
    <div class="p-golden-lg space-y-golden-lg">
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div class="flex items-center space-x-golden-md">
          <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-golden-400 rounded-lg flex items-center justify-center">
            <Package size={24} class="text-white" />
          </div>
          <div>
            <h1 class="text-3xl font-bold text-white">Inventory Management</h1>
            <p class="text-white/70">Track and manage your HVAC parts and supplies</p>
          </div>
        </div>
        
        <GoldenButton variant="cosmic" size="lg" glow physics>
          <Plus size={20} class="mr-golden-sm" />
          Add Item
        </GoldenButton>
      </div>

      <div
        class={`grid grid-cols-1 md:grid-cols-4 gap-golden-md transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="cosmic" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">1,337</div>
            <div class="text-white/70 text-sm">Total Items</div>
          </div>
        </CosmicCard>
        <CosmicCard variant="golden" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">$89,144</div>
            <div class="text-white/70 text-sm">Total Value</div>
          </div>
        </CosmicCard>
        <CosmicCard variant="divine" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">23</div>
            <div class="text-white/70 text-sm">Low Stock</div>
          </div>
        </CosmicCard>
        <CosmicCard variant="glass" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">156</div>
            <div class="text-white/70 text-sm">Categories</div>
          </div>
        </CosmicCard>
      </div>

      <CosmicCard variant="glass" size="lg" glow>
        <div class="text-center py-golden-xl">
          <Package size={64} class="text-white/30 mx-auto mb-golden-md" />
          <h3 class="text-2xl font-bold text-white mb-golden-sm">Inventory System</h3>
          <p class="text-white/70 mb-golden-lg max-w-md mx-auto">
            Advanced inventory management with real-time tracking, automated reordering, and cosmic-level precision.
          </p>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-golden-md">
            <GoldenButton variant="cosmic" size="lg" glow physics>
              <Search size={20} class="mr-golden-sm" />
              Search Items
            </GoldenButton>
            <GoldenButton variant="golden" size="lg" glow physics>
              <AlertTriangle size={20} class="mr-golden-sm" />
              Low Stock Alerts
            </GoldenButton>
            <GoldenButton variant="divine" size="lg" glow physics>
              <TrendingUp size={20} class="mr-golden-sm" />
              Analytics
            </GoldenButton>
          </div>
        </div>
      </CosmicCard>
    </div>
  )
}
