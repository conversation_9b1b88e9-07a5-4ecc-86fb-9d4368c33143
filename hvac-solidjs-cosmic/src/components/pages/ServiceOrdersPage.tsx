import { type Component, createSignal, onMount, For, Show, createMemo, createEffect } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import {
  Wrench,
  Plus,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  User,
  MapPin,
  DollarSign,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Activity,
  RefreshCw,
  Zap,
  Brain,
  Target,
  Star,
  TrendingUp,
  Users,
  MoreVertical,
  PlayCircle,
  PauseCircle,
  StopCircle,
  Settings,
  FileText,
  Camera,
  MessageSquare,
  XCircle
} from 'lucide-solid'
import { useJobs, useCreateJob, useUpdateJob, useDeleteJob, apiUtils } from '../../lib/api'
import type { ServiceJob, SearchFilters, PaginationParams } from '../../lib/api'
import { toast } from '../molecules/CosmicToast'

export const ServiceOrdersPage: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [searchTerm, setSearchTerm] = createSignal('')
  const [selectedStatus, setSelectedStatus] = createSignal('all')
  const [selectedPriority, setSelectedPriority] = createSignal('all')
  const [currentPage, setCurrentPage] = createSignal(1)
  const [pageSize, setPageSize] = createSignal(20)
  const [sortBy, setSortBy] = createSignal('scheduledDate')
  const [sortOrder, setSortOrder] = createSignal<'asc' | 'desc'>('desc')

  // 🌟 Real API Integration
  const filters = createMemo((): SearchFilters =>
    apiUtils.createFilters({
      query: searchTerm(),
      status: selectedStatus() === 'all' ? undefined : [selectedStatus()],
      // Add priority filter when API supports it
    })
  )

  const pagination = createMemo((): PaginationParams =>
    apiUtils.createPagination({
      page: currentPage(),
      limit: pageSize(),
      sortBy: sortBy(),
      sortOrder: sortOrder()
    })
  )

  const jobsQuery = useJobs(filters(), pagination())
  const createJobMutation = useCreateJob()
  const updateJobMutation = useUpdateJob()
  const deleteJobMutation = useDeleteJob()

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 200)
  })

  // 🔄 Auto-refresh when filters change
  createEffect(() => {
    if (searchTerm() || selectedStatus() || selectedPriority()) {
      setCurrentPage(1) // Reset to first page when filtering
    }
  })

  // 🎯 Job Actions
  const handleDeleteJob = async (jobId: string) => {
    if (confirm('Are you sure you want to delete this service order?')) {
      try {
        await deleteJobMutation.mutateAsync(jobId)
        toast.success('Service order deleted successfully!')
      } catch (error) {
        toast.error(apiUtils.formatError(error))
      }
    }
  }

  const handleUpdateJobStatus = async (jobId: string, status: ServiceJob['status']) => {
    try {
      await updateJobMutation.mutateAsync({ id: jobId, status })
      toast.success(`Service order ${status} successfully!`)
    } catch (error) {
      toast.error(apiUtils.formatError(error))
    }
  }

  // 📊 Dynamic job data from API or fallback
  const jobs = () => jobsQuery.data?.data || []
  const totalJobs = () => jobsQuery.data?.total || 0
  const totalPages = () => jobsQuery.data?.totalPages || 1

  // 🧠 AI-Powered Job Insights
  const getJobPriorityScore = (job: ServiceJob) => {
    // Simple scoring algorithm - in real app this would come from AI service
    const priorityScore = job.priority === 'urgent' ? 40 : job.priority === 'high' ? 30 : job.priority === 'medium' ? 20 : 10
    const statusScore = job.status === 'pending' ? 30 : job.status === 'in_progress' ? 20 : 10
    const timeScore = job.scheduledDate ? 20 : 0
    return Math.min(priorityScore + statusScore + timeScore, 100)
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-red-400'
    if (score >= 60) return 'text-orange-400'
    if (score >= 40) return 'text-yellow-400'
    return 'text-green-400'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return CheckCircle
      case 'in-progress':
        return Clock
      case 'pending':
        return AlertTriangle
      default:
        return Clock
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 bg-green-400/10'
      case 'in-progress':
        return 'text-blue-400 bg-blue-400/10'
      case 'pending':
        return 'text-yellow-400 bg-yellow-400/10'
      default:
        return 'text-gray-400 bg-gray-400/10'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-500'
      case 'high':
        return 'bg-orange-500'
      case 'medium':
        return 'bg-yellow-500'
      case 'low':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div class="p-golden-lg space-y-golden-lg">
      {/* Header */}
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div class="flex items-center space-x-golden-md">
          <div class="w-12 h-12 bg-gradient-to-r from-divine-400 to-cosmic-400 rounded-lg flex items-center justify-center">
            <Wrench size={24} class="text-white" />
          </div>
          <div>
            <h1 class="text-3xl font-bold text-white">Service Orders</h1>
            <p class="text-white/70">Manage and track service requests</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-golden-sm mt-golden-md lg:mt-0">
          {/* Real-time Status */}
          <div class="flex items-center space-x-golden-xs">
            <Show
              when={!jobsQuery.isLoading}
              fallback={
                <div class="flex items-center space-x-golden-xs text-yellow-400">
                  <RefreshCw size={14} class="animate-spin" />
                  <span class="text-xs">Loading...</span>
                </div>
              }
            >
              <div class="flex items-center space-x-golden-xs text-green-400">
                <Activity size={14} />
                <span class="text-xs">{totalJobs()} orders</span>
              </div>
            </Show>
          </div>

          <GoldenButton
            variant="cosmic"
            size="md"
            glow
            onClick={() => jobsQuery.refetch()}
            disabled={jobsQuery.isFetching}
          >
            <RefreshCw size={16} class={`mr-golden-xs ${jobsQuery.isFetching ? 'animate-spin' : ''}`} />
            Refresh
          </GoldenButton>

          <GoldenButton variant="divine" size="lg" glow physics>
            <Plus size={20} class="mr-golden-sm" />
            Create Service Order
          </GoldenButton>
        </div>
      </div>

      {/* Stats */}
      <div
        class={`grid grid-cols-1 md:grid-cols-4 gap-golden-md transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">24</div>
            <div class="text-white/70 text-sm">Total Orders</div>
          </div>
        </CosmicCard>
        <CosmicCard variant="cosmic" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">8</div>
            <div class="text-white/70 text-sm">In Progress</div>
          </div>
        </CosmicCard>
        <CosmicCard variant="golden" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">12</div>
            <div class="text-white/70 text-sm">Completed</div>
          </div>
        </CosmicCard>
        <CosmicCard variant="divine" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">4</div>
            <div class="text-white/70 text-sm">Pending</div>
          </div>
        </CosmicCard>
      </div>

      {/* Service Orders List */}
      <div
        class={`transition-all duration-1000 delay-400 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="lg" glow>
          <div class="space-y-golden-md">
            {serviceOrders.map((order) => {
              const StatusIcon = getStatusIcon(order.status)
              return (
                <div class="bg-white/5 rounded-lg p-golden-md border border-white/10 hover:border-white/20 transition-all duration-300 hover:bg-white/10">
                  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-golden-md lg:space-y-0">
                    {/* Order Info */}
                    <div class="flex-1">
                      <div class="flex items-center space-x-golden-md mb-golden-sm">
                        <div>
                          <h3 class="text-lg font-bold text-white">{order.id}</h3>
                          <p class="text-white/70">{order.service}</p>
                        </div>
                        <div class={`w-3 h-3 rounded-full ${getPriorityColor(order.priority)}`}></div>
                        <div class={`flex items-center space-x-golden-xs px-golden-sm py-golden-xs rounded-full ${getStatusColor(order.status)}`}>
                          <StatusIcon size={14} />
                          <span class="text-sm font-medium">{order.status}</span>
                        </div>
                      </div>
                      
                      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-sm text-sm">
                        <div class="flex items-center space-x-golden-xs text-white/70">
                          <User size={14} />
                          <span>{order.customer}</span>
                        </div>
                        <div class="flex items-center space-x-golden-xs text-white/70">
                          <Wrench size={14} />
                          <span>{order.technician}</span>
                        </div>
                        <div class="flex items-center space-x-golden-xs text-white/70">
                          <Calendar size={14} />
                          <span>{order.scheduledDate}</span>
                        </div>
                        <div class="flex items-center space-x-golden-xs text-white/70">
                          <MapPin size={14} />
                          <span>{order.location}</span>
                        </div>
                      </div>
                    </div>

                    {/* Value and Actions */}
                    <div class="flex items-center space-x-golden-lg">
                      <div class="text-center">
                        <div class="flex items-center space-x-golden-xs text-golden-300">
                          <DollarSign size={16} />
                          <span class="text-lg font-bold">{order.estimatedValue.toLocaleString()}</span>
                        </div>
                        <div class="text-white/70 text-xs">Estimated Value</div>
                      </div>
                      
                      <div class="flex items-center space-x-golden-xs">
                        <GoldenButton variant="cosmic" size="sm" glow>
                          View
                        </GoldenButton>
                        <GoldenButton variant="golden" size="sm" glow>
                          Edit
                        </GoldenButton>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CosmicCard>
      </div>

      {/* Quick Actions */}
      <div
        class={`transition-all duration-1000 delay-600 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="lg" glow>
          <h3 class="text-xl font-bold text-white mb-golden-md">Quick Actions</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-golden-md">
            <GoldenButton variant="cosmic" size="lg" glow physics class="h-16">
              <Calendar size={20} class="mr-golden-sm" />
              Schedule Service
            </GoldenButton>
            <GoldenButton variant="golden" size="lg" glow physics class="h-16">
              <Wrench size={20} class="mr-golden-sm" />
              Assign Technician
            </GoldenButton>
            <GoldenButton variant="divine" size="lg" glow physics class="h-16">
              <CheckCircle size={20} class="mr-golden-sm" />
              Complete Order
            </GoldenButton>
          </div>
        </CosmicCard>
      </div>
    </div>
  )
}
