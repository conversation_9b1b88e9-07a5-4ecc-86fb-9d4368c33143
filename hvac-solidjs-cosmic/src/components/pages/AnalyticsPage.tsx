import { type Component, createSignal, onMount } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { TrendingUp, BarChart3, PieChart, Activity, Download, Calendar } from 'lucide-solid'

export const AnalyticsPage: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 200)
  })

  return (
    <div class="p-golden-lg space-y-golden-lg">
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div class="flex items-center space-x-golden-md">
          <div class="w-12 h-12 bg-gradient-to-r from-divine-400 to-golden-400 rounded-lg flex items-center justify-center">
            <TrendingUp size={24} class="text-white" />
          </div>
          <div>
            <h1 class="text-3xl font-bold text-white">Analytics & Insights</h1>
            <p class="text-white/70">Data-driven insights powered by cosmic algorithms</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-golden-sm mt-golden-md lg:mt-0">
          <GoldenButton variant="cosmic" size="md" glow>
            <Calendar size={16} class="mr-golden-xs" />
            Date Range
          </GoldenButton>
          <GoldenButton variant="golden" size="md" glow>
            <Download size={16} class="mr-golden-xs" />
            Export Report
          </GoldenButton>
        </div>
      </div>

      <div
        class={`grid grid-cols-1 md:grid-cols-3 gap-golden-md transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="cosmic" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">+137%</div>
            <div class="text-white/70 text-sm">Revenue Growth</div>
          </div>
        </CosmicCard>
        <CosmicCard variant="golden" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">98.6%</div>
            <div class="text-white/70 text-sm">Customer Satisfaction</div>
          </div>
        </CosmicCard>
        <CosmicCard variant="divine" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">23.4h</div>
            <div class="text-white/70 text-sm">Avg Response Time</div>
          </div>
        </CosmicCard>
      </div>

      <div
        class={`grid grid-cols-1 lg:grid-cols-2 gap-golden-lg transition-all duration-1000 delay-400 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="lg" glow>
          <h3 class="text-xl font-bold text-white mb-golden-md">Revenue Analytics</h3>
          <div class="h-64 bg-white/5 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <BarChart3 size={48} class="text-white/30 mx-auto mb-golden-sm" />
              <p class="text-white/50">Advanced charts coming soon</p>
            </div>
          </div>
        </CosmicCard>

        <CosmicCard variant="glass" size="lg" glow>
          <h3 class="text-xl font-bold text-white mb-golden-md">Service Distribution</h3>
          <div class="h-64 bg-white/5 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <PieChart size={48} class="text-white/30 mx-auto mb-golden-sm" />
              <p class="text-white/50">Cosmic visualizations loading</p>
            </div>
          </div>
        </CosmicCard>
      </div>

      <CosmicCard variant="glass" size="lg" glow>
        <div class="text-center py-golden-xl">
          <Activity size={64} class="text-white/30 mx-auto mb-golden-md" />
          <h3 class="text-2xl font-bold text-white mb-golden-sm">Advanced Analytics Engine</h3>
          <p class="text-white/70 mb-golden-lg max-w-2xl mx-auto">
            Powered by 137 cosmic algorithms, our analytics engine provides deep insights into your HVAC business performance with quantum-level precision.
          </p>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-golden-md">
            <GoldenButton variant="cosmic" size="lg" glow physics>
              <TrendingUp size={20} class="mr-golden-sm" />
              Predictive Analytics
            </GoldenButton>
            <GoldenButton variant="golden" size="lg" glow physics>
              <BarChart3 size={20} class="mr-golden-sm" />
              Custom Reports
            </GoldenButton>
            <GoldenButton variant="divine" size="lg" glow physics>
              <Activity size={20} class="mr-golden-sm" />
              Real-time Monitoring
            </GoldenButton>
          </div>
        </div>
      </CosmicCard>
    </div>
  )
}
