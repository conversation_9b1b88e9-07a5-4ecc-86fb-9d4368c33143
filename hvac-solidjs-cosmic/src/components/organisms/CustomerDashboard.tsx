import { type Component, createSignal, onMount, For } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { StatsGrid, type StatItem } from '../molecules/StatsGrid'
import { DataTable, type Column } from '../molecules/DataTable'
import { 
  Users, 
  TrendingUp, 
  DollarSign, 
  Calendar,
  Phone,
  Mail,
  MapPin,
  Star,
  Clock,
  Plus
} from 'lucide-solid'

interface Customer {
  id: string
  name: string
  email: string
  phone: string
  location: string
  status: 'active' | 'inactive' | 'pending'
  totalOrders: number
  totalValue: number
  lastContact: string
  rating: number
}

export const CustomerDashboard: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [customers, setCustomers] = createSignal<Customer[]>([])

  onMount(() => {
    // Simulate loading data
    setTimeout(() => {
      setCustomers([
        {
          id: 'CUST-001',
          name: 'Acme Corporation',
          email: '<EMAIL>',
          phone: '+****************',
          location: 'New York, NY',
          status: 'active',
          totalOrders: 15,
          totalValue: 45000,
          lastContact: '2024-01-15',
          rating: 4.8
        },
        {
          id: 'CUST-002',
          name: 'Tech Solutions Inc',
          email: '<EMAIL>',
          phone: '+****************',
          location: 'San Francisco, CA',
          status: 'active',
          totalOrders: 8,
          totalValue: 28000,
          lastContact: '2024-01-14',
          rating: 4.5
        },
        {
          id: 'CUST-003',
          name: 'Global Industries',
          email: '<EMAIL>',
          phone: '+****************',
          location: 'Chicago, IL',
          status: 'pending',
          totalOrders: 3,
          totalValue: 12000,
          lastContact: '2024-01-10',
          rating: 4.2
        }
      ])
      setIsLoaded(true)
    }, 500)
  })

  const stats: StatItem[] = [
    {
      id: 'total-customers',
      title: 'Total Customers',
      value: 1337,
      change: { value: 12, type: 'increase', period: 'this month' },
      icon: Users,
      color: 'cosmic',
      format: 'number'
    },
    {
      id: 'active-customers',
      title: 'Active Customers',
      value: 1156,
      change: { value: 8, type: 'increase', period: 'this week' },
      icon: TrendingUp,
      color: 'golden',
      format: 'number'
    },
    {
      id: 'total-revenue',
      title: 'Total Revenue',
      value: 2618000,
      change: { value: 15.7, type: 'increase', period: 'vs last month' },
      icon: DollarSign,
      color: 'divine',
      format: 'currency'
    },
    {
      id: 'avg-response-time',
      title: 'Avg Response Time',
      value: 137,
      change: { value: -23, type: 'decrease', period: 'minutes' },
      icon: Clock,
      color: 'cosmic',
      format: 'time',
      description: 'Faster response times improve satisfaction'
    }
  ]

  const columns: Column<Customer>[] = [
    {
      key: 'name',
      label: 'Customer',
      sortable: true,
      render: (value, row) => (
        <div class="flex items-center space-x-golden-sm">
          <div class="w-8 h-8 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-full flex items-center justify-center">
            <span class="text-white text-sm font-bold">{value.charAt(0)}</span>
          </div>
          <div>
            <div class="font-medium text-white">{value}</div>
            <div class="text-white/60 text-sm">{row.id}</div>
          </div>
        </div>
      )
    },
    {
      key: 'email',
      label: 'Contact',
      render: (value, row) => (
        <div class="space-y-1">
          <div class="flex items-center space-x-golden-xs text-white/80 text-sm">
            <Mail size={12} />
            <span>{value}</span>
          </div>
          <div class="flex items-center space-x-golden-xs text-white/80 text-sm">
            <Phone size={12} />
            <span>{row.phone}</span>
          </div>
        </div>
      )
    },
    {
      key: 'location',
      label: 'Location',
      render: (value) => (
        <div class="flex items-center space-x-golden-xs text-white/80">
          <MapPin size={14} />
          <span>{value}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => {
        const colors = {
          active: 'bg-green-500/20 text-green-400',
          pending: 'bg-yellow-500/20 text-yellow-400',
          inactive: 'bg-red-500/20 text-red-400'
        }
        return (
          <span class={`px-golden-sm py-golden-xs rounded-full text-xs font-medium ${colors[value as keyof typeof colors]}`}>
            {value.charAt(0).toUpperCase() + value.slice(1)}
          </span>
        )
      }
    },
    {
      key: 'totalOrders',
      label: 'Orders',
      sortable: true,
      render: (value) => (
        <span class="font-medium text-white">{value}</span>
      )
    },
    {
      key: 'totalValue',
      label: 'Value',
      sortable: true,
      render: (value) => (
        <span class="font-medium text-golden-300">${value.toLocaleString()}</span>
      )
    },
    {
      key: 'rating',
      label: 'Rating',
      sortable: true,
      render: (value) => (
        <div class="flex items-center space-x-golden-xs">
          <Star size={14} class="text-yellow-400 fill-current" />
          <span class="text-white font-medium">{value}</span>
        </div>
      )
    }
  ]

  return (
    <div class="space-y-golden-lg">
      {/* Header */}
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div>
          <h2 class="text-2xl font-bold text-white mb-golden-sm">Customer Overview</h2>
          <p class="text-white/70">Comprehensive customer relationship management</p>
        </div>
        
        <GoldenButton variant="cosmic" size="lg" glow physics>
          <Plus size={20} class="mr-golden-sm" />
          Add Customer
        </GoldenButton>
      </div>

      {/* Stats Grid */}
      <div
        class={`transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <StatsGrid stats={stats} columns={4} animated />
      </div>

      {/* Recent Activity */}
      <div
        class={`grid grid-cols-1 lg:grid-cols-3 gap-golden-lg transition-all duration-1000 delay-400 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        {/* Top Customers */}
        <CosmicCard variant="cosmic" size="lg" glow>
          <h3 class="text-lg font-bold text-white mb-golden-md">Top Customers</h3>
          <div class="space-y-golden-sm">
            <For each={customers().slice(0, 3)}>
              {(customer) => (
                <div class="flex items-center justify-between p-golden-sm bg-white/5 rounded-lg">
                  <div class="flex items-center space-x-golden-sm">
                    <div class="w-8 h-8 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-full flex items-center justify-center">
                      <span class="text-white text-sm font-bold">{customer.name.charAt(0)}</span>
                    </div>
                    <div>
                      <div class="text-white font-medium text-sm">{customer.name}</div>
                      <div class="text-white/60 text-xs">{customer.totalOrders} orders</div>
                    </div>
                  </div>
                  <div class="text-golden-300 font-bold text-sm">
                    ${customer.totalValue.toLocaleString()}
                  </div>
                </div>
              )}
            </For>
          </div>
        </CosmicCard>

        {/* Recent Contacts */}
        <CosmicCard variant="golden" size="lg" glow>
          <h3 class="text-lg font-bold text-white mb-golden-md">Recent Contacts</h3>
          <div class="space-y-golden-sm">
            <For each={customers().slice(0, 3)}>
              {(customer) => (
                <div class="flex items-center justify-between p-golden-sm bg-white/5 rounded-lg">
                  <div class="flex items-center space-x-golden-sm">
                    <Calendar size={16} class="text-golden-400" />
                    <div>
                      <div class="text-white font-medium text-sm">{customer.name}</div>
                      <div class="text-white/60 text-xs">{customer.lastContact}</div>
                    </div>
                  </div>
                  <div class="flex items-center space-x-golden-xs">
                    <Star size={12} class="text-yellow-400 fill-current" />
                    <span class="text-white text-sm">{customer.rating}</span>
                  </div>
                </div>
              )}
            </For>
          </div>
        </CosmicCard>

        {/* Quick Actions */}
        <CosmicCard variant="divine" size="lg" glow>
          <h3 class="text-lg font-bold text-white mb-golden-md">Quick Actions</h3>
          <div class="space-y-golden-sm">
            <GoldenButton variant="cosmic" size="md" glow class="w-full justify-start">
              <Users size={16} class="mr-golden-sm" />
              Import Customers
            </GoldenButton>
            <GoldenButton variant="golden" size="md" glow class="w-full justify-start">
              <Mail size={16} class="mr-golden-sm" />
              Send Newsletter
            </GoldenButton>
            <GoldenButton variant="divine" size="md" glow class="w-full justify-start">
              <TrendingUp size={16} class="mr-golden-sm" />
              Generate Report
            </GoldenButton>
          </div>
        </CosmicCard>
      </div>

      {/* Customer Table */}
      <div
        class={`transition-all duration-1000 delay-600 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <DataTable
          data={customers()}
          columns={columns}
          searchable
          filterable
          exportable
          loading={!isLoaded()}
          onRowClick={(customer) => console.log('View customer:', customer)}
          onSearch={(term) => console.log('Search:', term)}
          onExport={() => console.log('Export customers')}
        />
      </div>
    </div>
  )
}
