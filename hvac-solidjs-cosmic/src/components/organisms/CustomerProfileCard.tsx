// 🏢 CUSTOMER PROFILE CARD - Comprehensive Customer Intelligence
// Advanced customer profile with AI insights and complete history

import { type Component, createSignal, Show, For } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Building,
  Calendar,
  DollarSign,
  Wrench,
  Star,
  TrendingUp,
  TrendingDown,
  Activity,
  Brain,
  Target,
  Tag,
  Clock,
  CheckCircle,
  AlertTriangle,
  MessageSquare,
  FileText,
  Camera,
  Edit,
  MoreVertical,
  Zap,
  Award,
  Heart
} from 'lucide-solid'
import type { Customer } from '../../lib/api'

interface CustomerProfileProps {
  customer: Customer
  onEdit?: () => void
  onMessage?: () => void
  onCreateJob?: () => void
}

interface CustomerInsight {
  type: 'satisfaction' | 'value' | 'risk' | 'opportunity'
  label: string
  value: string
  trend: 'up' | 'down' | 'stable'
  confidence: number
}

interface ServiceHistory {
  id: string
  date: string
  service: string
  technician: string
  status: 'completed' | 'in_progress' | 'cancelled'
  value: number
  rating?: number
}

export const CustomerProfileCard: Component<CustomerProfileProps> = (props) => {
  const [activeTab, setActiveTab] = createSignal('overview')

  // 🧠 AI-Generated Customer Insights
  const customerInsights = (): CustomerInsight[] => [
    {
      type: 'satisfaction',
      label: 'Satisfaction Score',
      value: '96%',
      trend: 'up',
      confidence: 89
    },
    {
      type: 'value',
      label: 'Lifetime Value',
      value: '$45,200',
      trend: 'up',
      confidence: 92
    },
    {
      type: 'risk',
      label: 'Churn Risk',
      value: 'Low (8%)',
      trend: 'down',
      confidence: 87
    },
    {
      type: 'opportunity',
      label: 'Upsell Potential',
      value: 'High (78%)',
      trend: 'up',
      confidence: 84
    }
  ]

  // 📋 Service History
  const serviceHistory = (): ServiceHistory[] => [
    {
      id: 'SH-001',
      date: '2024-01-15',
      service: 'AC Installation',
      technician: 'John Smith',
      status: 'completed',
      value: 2500,
      rating: 5
    },
    {
      id: 'SH-002',
      date: '2024-01-10',
      service: 'HVAC Maintenance',
      technician: 'Sarah Johnson',
      status: 'completed',
      value: 350,
      rating: 4
    },
    {
      id: 'SH-003',
      date: '2024-01-05',
      service: 'System Inspection',
      technician: 'Mike Wilson',
      status: 'completed',
      value: 150,
      rating: 5
    }
  ]

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'satisfaction':
        return Heart
      case 'value':
        return DollarSign
      case 'risk':
        return AlertTriangle
      case 'opportunity':
        return Target
      default:
        return Star
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'satisfaction':
        return 'from-pink-400 to-rose-600'
      case 'value':
        return 'from-green-400 to-emerald-600'
      case 'risk':
        return 'from-red-400 to-red-600'
      case 'opportunity':
        return 'from-blue-400 to-blue-600'
      default:
        return 'from-cosmic-400 to-cosmic-600'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 bg-green-400/10'
      case 'in_progress':
        return 'text-blue-400 bg-blue-400/10'
      case 'cancelled':
        return 'text-red-400 bg-red-400/10'
      default:
        return 'text-gray-400 bg-gray-400/10'
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star 
        size={12} 
        class={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-400'} 
      />
    ))
  }

  return (
    <CosmicCard variant="glass" size="lg" glow>
      {/* Customer Header */}
      <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-golden-md lg:space-y-0 mb-golden-lg">
        <div class="flex items-start space-x-golden-md">
          <div class="w-16 h-16 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-full flex items-center justify-center">
            <User size={32} class="text-white" />
          </div>
          
          <div class="flex-1">
            <div class="flex items-center space-x-golden-sm mb-golden-xs">
              <h2 class="text-2xl font-bold text-white">{props.customer.name}</h2>
              <div class="flex items-center space-x-golden-xs">
                <Brain size={14} class="text-purple-400" />
                <span class="text-purple-400 text-sm font-bold">AI Score: 94</span>
              </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-golden-sm text-sm">
              <div class="flex items-center space-x-golden-xs text-white/70">
                <Mail size={14} />
                <span>{props.customer.email}</span>
              </div>
              <div class="flex items-center space-x-golden-xs text-white/70">
                <Phone size={14} />
                <span>{props.customer.phone || 'No phone'}</span>
              </div>
              <div class="flex items-center space-x-golden-xs text-white/70">
                <MapPin size={14} />
                <span>{props.customer.address || 'No address'}</span>
              </div>
              <div class="flex items-center space-x-golden-xs text-white/70">
                <Building size={14} />
                <span>{props.customer.company || 'Individual'}</span>
              </div>
            </div>

            {/* Tags */}
            <Show when={props.customer.tags && props.customer.tags.length > 0}>
              <div class="flex items-center space-x-golden-xs mt-golden-sm">
                <Tag size={12} class="text-white/50" />
                <div class="flex flex-wrap gap-1">
                  <For each={props.customer.tags}>
                    {(tag) => (
                      <span class="px-2 py-0.5 bg-cosmic-500/20 text-cosmic-300 text-xs rounded-full">
                        {tag}
                      </span>
                    )}
                  </For>
                </div>
              </div>
            </Show>
          </div>
        </div>

        {/* Action Buttons */}
        <div class="flex items-center space-x-golden-sm">
          <GoldenButton 
            variant="cosmic" 
            size="sm" 
            glow
            onClick={props.onMessage}
          >
            <MessageSquare size={14} class="mr-golden-xs" />
            Message
          </GoldenButton>
          <GoldenButton 
            variant="golden" 
            size="sm" 
            glow
            onClick={props.onCreateJob}
          >
            <Wrench size={14} class="mr-golden-xs" />
            New Job
          </GoldenButton>
          <GoldenButton 
            variant="divine" 
            size="sm" 
            glow
            onClick={props.onEdit}
          >
            <Edit size={14} class="mr-golden-xs" />
            Edit
          </GoldenButton>
          <GoldenButton variant="glass" size="sm" glow>
            <MoreVertical size={14} />
          </GoldenButton>
        </div>
      </div>

      {/* Tab Navigation */}
      <div class="flex items-center space-x-golden-md mb-golden-lg border-b border-white/10">
        {['overview', 'history', 'insights'].map((tab) => (
          <button
            class={`pb-golden-sm text-sm font-medium transition-colors ${
              activeTab() === tab 
                ? 'text-cosmic-400 border-b-2 border-cosmic-400' 
                : 'text-white/70 hover:text-white'
            }`}
            onClick={() => setActiveTab(tab)}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <Show when={activeTab() === 'overview'}>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md">
          <For each={customerInsights()}>
            {(insight) => {
              const Icon = getInsightIcon(insight.type)
              return (
                <div class="bg-white/5 rounded-lg p-golden-md border border-white/10">
                  <div class="flex items-center justify-between mb-golden-sm">
                    <div class={`w-8 h-8 bg-gradient-to-r ${getInsightColor(insight.type)} rounded-lg flex items-center justify-center`}>
                      <Icon size={16} class="text-white" />
                    </div>
                    <div class="flex items-center space-x-golden-xs">
                      {insight.trend === 'up' ? (
                        <TrendingUp size={12} class="text-green-400" />
                      ) : insight.trend === 'down' ? (
                        <TrendingDown size={12} class="text-red-400" />
                      ) : (
                        <Activity size={12} class="text-yellow-400" />
                      )}
                    </div>
                  </div>
                  <div class="text-lg font-bold text-white mb-golden-xs">{insight.value}</div>
                  <div class="text-white/70 text-sm mb-golden-xs">{insight.label}</div>
                  <div class="text-white/50 text-xs">{insight.confidence}% confidence</div>
                </div>
              )
            }}
          </For>
        </div>
      </Show>

      <Show when={activeTab() === 'history'}>
        <div class="space-y-golden-md">
          <For each={serviceHistory()}>
            {(service) => (
              <div class="bg-white/5 rounded-lg p-golden-md border border-white/10">
                <div class="flex items-center justify-between mb-golden-sm">
                  <div>
                    <h4 class="text-lg font-bold text-white">{service.service}</h4>
                    <p class="text-white/70 text-sm">{service.id} • {service.technician}</p>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-bold text-white">${service.value.toLocaleString()}</div>
                    <div class="text-white/70 text-sm">{service.date}</div>
                  </div>
                </div>
                
                <div class="flex items-center justify-between">
                  <div class={`flex items-center space-x-golden-xs px-golden-sm py-golden-xs rounded-full ${getStatusColor(service.status)}`}>
                    <CheckCircle size={12} />
                    <span class="text-sm font-medium">{service.status}</span>
                  </div>
                  
                  <Show when={service.rating}>
                    <div class="flex items-center space-x-golden-xs">
                      <div class="flex space-x-0.5">
                        {renderStars(service.rating!)}
                      </div>
                      <span class="text-white/70 text-sm">({service.rating}/5)</span>
                    </div>
                  </Show>
                </div>
              </div>
            )}
          </For>
        </div>
      </Show>

      <Show when={activeTab() === 'insights'}>
        <div class="space-y-golden-md">
          <div class="bg-white/5 rounded-lg p-golden-md border border-white/10">
            <div class="flex items-center space-x-golden-sm mb-golden-md">
              <Brain size={20} class="text-purple-400" />
              <h4 class="text-lg font-bold text-white">AI-Powered Insights</h4>
            </div>
            
            <div class="space-y-golden-sm text-white/80">
              <p>• This customer has a 94% satisfaction rate and shows high loyalty indicators</p>
              <p>• Optimal time for next service contact: within 2-3 weeks</p>
              <p>• High probability (78%) of accepting premium service upgrades</p>
              <p>• Prefers morning appointments and detailed service explanations</p>
              <p>• Strong referral potential - has referred 3 customers in the past year</p>
            </div>
            
            <div class="mt-golden-md">
              <GoldenButton variant="cosmic" size="sm" glow>
                <Zap size={14} class="mr-golden-xs" />
                Generate Action Plan
              </GoldenButton>
            </div>
          </div>
        </div>
      </Show>
    </CosmicCard>
  )
}
