// 🧠 AI INSIGHTS PANEL - Cosmic Intelligence for HVAC CRM
// Advanced AI-powered analytics and recommendations

import { type Component, createSignal, onMount, Show, For } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import {
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Target,
  Zap,
  Star,
  Activity,
  BarChart3,
  PieChart,
  Calendar,
  Users,
  DollarSign,
  Wrench,
  RefreshCw,
  Lightbulb,
  Award,
  Clock
} from 'lucide-solid'
import { useAnalyzeText, useDashboardMetrics } from '../../lib/api'

interface AIInsight {
  id: string
  type: 'opportunity' | 'warning' | 'recommendation' | 'trend'
  title: string
  description: string
  confidence: number
  impact: 'high' | 'medium' | 'low'
  actionable: boolean
  category: 'customer' | 'service' | 'financial' | 'operational'
}

interface AIMetric {
  label: string
  value: string | number
  change: number
  trend: 'up' | 'down' | 'stable'
  confidence: number
}

export const AIInsightsPanel: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [selectedCategory, setSelectedCategory] = createSignal('all')
  const [isAnalyzing, setIsAnalyzing] = createSignal(false)

  const dashboardMetrics = useDashboardMetrics()
  const analyzeTextMutation = useAnalyzeText()

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 300)
  })

  // 🧠 Mock AI Insights - In real app, these would come from AI service
  const aiInsights = (): AIInsight[] => [
    {
      id: 'insight-1',
      type: 'opportunity',
      title: 'High-Value Customer Opportunity',
      description: 'Customer "Acme Corp" shows 85% likelihood of requiring premium HVAC upgrade within 30 days based on service history patterns.',
      confidence: 87,
      impact: 'high',
      actionable: true,
      category: 'customer'
    },
    {
      id: 'insight-2',
      type: 'warning',
      title: 'Service Capacity Alert',
      description: 'Current technician workload is 94% capacity. Consider scheduling optimization or additional resources.',
      confidence: 92,
      impact: 'high',
      actionable: true,
      category: 'operational'
    },
    {
      id: 'insight-3',
      type: 'trend',
      title: 'Seasonal Demand Pattern',
      description: 'AC maintenance requests typically increase by 340% in the next 4 weeks. Prepare inventory and scheduling.',
      confidence: 78,
      impact: 'medium',
      actionable: true,
      category: 'service'
    },
    {
      id: 'insight-4',
      type: 'recommendation',
      title: 'Revenue Optimization',
      description: 'Bundling preventive maintenance with repairs could increase average order value by 23%.',
      confidence: 81,
      impact: 'medium',
      actionable: true,
      category: 'financial'
    }
  ]

  // 🎯 AI-Powered Metrics
  const aiMetrics = (): AIMetric[] => [
    {
      label: 'Customer Satisfaction Score',
      value: '94.2%',
      change: 5.7,
      trend: 'up',
      confidence: 89
    },
    {
      label: 'Predicted Revenue (30d)',
      value: '$187,500',
      change: 12.3,
      trend: 'up',
      confidence: 76
    },
    {
      label: 'Service Efficiency Index',
      value: '87.4',
      change: -2.1,
      trend: 'down',
      confidence: 91
    },
    {
      label: 'Lead Conversion Rate',
      value: '68.9%',
      change: 8.4,
      trend: 'up',
      confidence: 83
    }
  ]

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity':
        return Target
      case 'warning':
        return AlertTriangle
      case 'recommendation':
        return Lightbulb
      case 'trend':
        return TrendingUp
      default:
        return Brain
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'opportunity':
        return 'from-green-400 to-emerald-600'
      case 'warning':
        return 'from-red-400 to-red-600'
      case 'recommendation':
        return 'from-blue-400 to-blue-600'
      case 'trend':
        return 'from-purple-400 to-purple-600'
      default:
        return 'from-cosmic-400 to-cosmic-600'
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'text-red-400'
      case 'medium':
        return 'text-yellow-400'
      case 'low':
        return 'text-green-400'
      default:
        return 'text-gray-400'
    }
  }

  const filteredInsights = () => {
    if (selectedCategory() === 'all') return aiInsights()
    return aiInsights().filter(insight => insight.category === selectedCategory())
  }

  const runAIAnalysis = async () => {
    setIsAnalyzing(true)
    try {
      // In real app, this would trigger comprehensive AI analysis
      await new Promise(resolve => setTimeout(resolve, 2000))
      // Simulate AI analysis results
      console.log('AI Analysis completed')
    } catch (error) {
      console.error('AI Analysis failed:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  return (
    <div class="space-y-golden-lg">
      {/* AI Insights Header */}
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div class="flex items-center space-x-golden-md">
          <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-600 rounded-lg flex items-center justify-center">
            <Brain size={24} class="text-white" />
          </div>
          <div>
            <h2 class="text-2xl font-bold text-white">AI Insights</h2>
            <p class="text-white/70">Powered by cosmic intelligence</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-golden-sm mt-golden-md lg:mt-0">
          <select
            class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg px-golden-md py-golden-sm text-white focus:outline-none focus:ring-2 focus:ring-cosmic-400"
            value={selectedCategory()}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            <option value="all">All Categories</option>
            <option value="customer">Customer</option>
            <option value="service">Service</option>
            <option value="financial">Financial</option>
            <option value="operational">Operational</option>
          </select>
          
          <GoldenButton 
            variant="cosmic" 
            size="md" 
            glow
            onClick={runAIAnalysis}
            disabled={isAnalyzing()}
          >
            <Brain size={16} class={`mr-golden-xs ${isAnalyzing() ? 'animate-pulse' : ''}`} />
            {isAnalyzing() ? 'Analyzing...' : 'Run Analysis'}
          </GoldenButton>
        </div>
      </div>

      {/* AI Metrics */}
      <div
        class={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <For each={aiMetrics()}>
          {(metric) => (
            <CosmicCard variant="glass" size="md" glow hover3d>
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="text-lg font-bold text-white mb-golden-xs">
                    {metric.value}
                  </div>
                  <div class="text-white/70 text-sm mb-golden-xs">{metric.label}</div>
                  <div class="flex items-center space-x-golden-xs">
                    {metric.trend === 'up' ? (
                      <TrendingUp size={12} class="text-green-400" />
                    ) : metric.trend === 'down' ? (
                      <TrendingDown size={12} class="text-red-400" />
                    ) : (
                      <Activity size={12} class="text-yellow-400" />
                    )}
                    <span class={`text-xs ${
                      metric.trend === 'up' ? 'text-green-400' : 
                      metric.trend === 'down' ? 'text-red-400' : 'text-yellow-400'
                    }`}>
                      {metric.change > 0 ? '+' : ''}{metric.change.toFixed(1)}%
                    </span>
                    <span class="text-white/50 text-xs">({metric.confidence}% confidence)</span>
                  </div>
                </div>
                <div class="w-10 h-10 bg-gradient-to-r from-purple-400 to-pink-600 rounded-lg flex items-center justify-center">
                  <Star size={20} class="text-white" />
                </div>
              </div>
            </CosmicCard>
          )}
        </For>
      </div>

      {/* AI Insights List */}
      <div
        class={`transition-all duration-1000 delay-400 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="lg" glow>
          <div class="flex items-center justify-between mb-golden-md">
            <h3 class="text-xl font-bold text-white">Intelligent Recommendations</h3>
            <div class="flex items-center space-x-golden-xs text-white/70">
              <Brain size={16} />
              <span class="text-sm">{filteredInsights().length} insights</span>
            </div>
          </div>

          <div class="space-y-golden-md">
            <For each={filteredInsights()}>
              {(insight) => {
                const Icon = getInsightIcon(insight.type)
                return (
                  <div class="bg-white/5 rounded-lg p-golden-md border border-white/10 hover:border-white/20 transition-all duration-300 hover:bg-white/10">
                    <div class="flex items-start space-x-golden-md">
                      <div class={`w-10 h-10 bg-gradient-to-r ${getInsightColor(insight.type)} rounded-lg flex items-center justify-center flex-shrink-0`}>
                        <Icon size={20} class="text-white" />
                      </div>
                      
                      <div class="flex-1">
                        <div class="flex items-center space-x-golden-sm mb-golden-xs">
                          <h4 class="text-lg font-bold text-white">{insight.title}</h4>
                          <span class={`text-xs px-2 py-1 rounded-full ${getImpactColor(insight.impact)} bg-current/10`}>
                            {insight.impact} impact
                          </span>
                          <span class="text-white/50 text-xs">{insight.confidence}% confidence</span>
                        </div>
                        
                        <p class="text-white/80 text-sm mb-golden-sm">{insight.description}</p>
                        
                        <div class="flex items-center justify-between">
                          <div class="flex items-center space-x-golden-sm">
                            <span class="text-white/60 text-xs capitalize">{insight.category}</span>
                            <span class="text-white/60 text-xs">•</span>
                            <span class="text-white/60 text-xs capitalize">{insight.type}</span>
                          </div>
                          
                          <Show when={insight.actionable}>
                            <GoldenButton variant="cosmic" size="sm" glow>
                              <Zap size={12} class="mr-golden-xs" />
                              Take Action
                            </GoldenButton>
                          </Show>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              }}
            </For>
          </div>
        </CosmicCard>
      </div>
    </div>
  )
}
