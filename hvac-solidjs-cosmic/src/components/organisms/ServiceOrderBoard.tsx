import { type Component, createSignal, onMount, For } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  User, 
  Calendar,
  MapPin,
  DollarSign,
  Plus,
  Filter,
  Search
} from 'lucide-solid'

interface ServiceOrder {
  id: string
  title: string
  customer: string
  technician: string
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  scheduledDate: string
  estimatedValue: number
  location: string
  description: string
  tags: string[]
}

const STATUSES = [
  { key: 'pending', label: 'Pending', icon: AlertTriangle, color: 'bg-yellow-500' },
  { key: 'in-progress', label: 'In Progress', icon: Clock, color: 'bg-blue-500' },
  { key: 'completed', label: 'Completed', icon: CheckCircle, color: 'bg-green-500' },
  { key: 'cancelled', label: 'Cancelled', icon: AlertTriangle, color: 'bg-red-500' }
]

export const ServiceOrderBoard: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [orders, setOrders] = createSignal<ServiceOrder[]>([])
  const [searchTerm, setSearchTerm] = createSignal('')
  const [selectedStatus, setSelectedStatus] = createSignal<string>('all')

  onMount(() => {
    // Simulate loading data
    setTimeout(() => {
      setOrders([
        {
          id: 'SO-001',
          title: 'AC Installation',
          customer: 'Acme Corporation',
          technician: 'John Smith',
          status: 'pending',
          priority: 'high',
          scheduledDate: '2024-01-16',
          estimatedValue: 2500,
          location: 'New York, NY',
          description: 'Install new central air conditioning system',
          tags: ['installation', 'commercial']
        },
        {
          id: 'SO-002',
          title: 'HVAC Maintenance',
          customer: 'Tech Solutions',
          technician: 'Sarah Johnson',
          status: 'in-progress',
          priority: 'medium',
          scheduledDate: '2024-01-15',
          estimatedValue: 800,
          location: 'San Francisco, CA',
          description: 'Routine maintenance and filter replacement',
          tags: ['maintenance', 'routine']
        },
        {
          id: 'SO-003',
          title: 'System Repair',
          customer: 'Global Industries',
          technician: 'Mike Wilson',
          status: 'completed',
          priority: 'urgent',
          scheduledDate: '2024-01-14',
          estimatedValue: 1200,
          location: 'Chicago, IL',
          description: 'Emergency repair of heating system',
          tags: ['repair', 'emergency']
        },
        {
          id: 'SO-004',
          title: 'Duct Cleaning',
          customer: 'Local Business',
          technician: 'Emily Davis',
          status: 'pending',
          priority: 'low',
          scheduledDate: '2024-01-17',
          estimatedValue: 600,
          location: 'Austin, TX',
          description: 'Professional duct cleaning service',
          tags: ['cleaning', 'maintenance']
        }
      ])
      setIsLoaded(true)
    }, 500)
  })

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-500'
      case 'high':
        return 'bg-orange-500'
      case 'medium':
        return 'bg-yellow-500'
      case 'low':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    const statusConfig = STATUSES.find(s => s.key === status)
    return statusConfig?.icon || Clock
  }

  const getStatusColor = (status: string) => {
    const statusConfig = STATUSES.find(s => s.key === status)
    return statusConfig?.color || 'bg-gray-500'
  }

  const filteredOrders = () => {
    return orders().filter(order => {
      const matchesSearch = order.title.toLowerCase().includes(searchTerm().toLowerCase()) ||
                           order.customer.toLowerCase().includes(searchTerm().toLowerCase()) ||
                           order.technician.toLowerCase().includes(searchTerm().toLowerCase())
      const matchesStatus = selectedStatus() === 'all' || order.status === selectedStatus()
      return matchesSearch && matchesStatus
    })
  }

  const getOrdersByStatus = (status: string) => {
    return filteredOrders().filter(order => order.status === status)
  }

  const OrderCard: Component<{ order: ServiceOrder }> = ({ order }) => {
    const StatusIcon = getStatusIcon(order.status)
    
    return (
      <CosmicCard variant="glass" size="md" glow hover3d physics class="cursor-pointer group">
        <div class="space-y-golden-sm">
          {/* Header */}
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h4 class="font-bold text-white group-hover:text-cosmic-300 transition-colors">
                {order.title}
              </h4>
              <p class="text-white/60 text-sm">{order.id}</p>
            </div>
            <div class={`w-3 h-3 rounded-full ${getPriorityColor(order.priority)}`} />
          </div>

          {/* Customer & Technician */}
          <div class="space-y-golden-xs">
            <div class="flex items-center space-x-golden-xs text-white/80 text-sm">
              <User size={12} />
              <span>{order.customer}</span>
            </div>
            <div class="flex items-center space-x-golden-xs text-white/80 text-sm">
              <User size={12} />
              <span>{order.technician}</span>
            </div>
          </div>

          {/* Details */}
          <div class="space-y-golden-xs">
            <div class="flex items-center space-x-golden-xs text-white/70 text-sm">
              <Calendar size={12} />
              <span>{order.scheduledDate}</span>
            </div>
            <div class="flex items-center space-x-golden-xs text-white/70 text-sm">
              <MapPin size={12} />
              <span>{order.location}</span>
            </div>
            <div class="flex items-center space-x-golden-xs text-golden-300 text-sm font-medium">
              <DollarSign size={12} />
              <span>${order.estimatedValue.toLocaleString()}</span>
            </div>
          </div>

          {/* Description */}
          <p class="text-white/60 text-sm line-clamp-2">
            {order.description}
          </p>

          {/* Tags */}
          <div class="flex flex-wrap gap-golden-xs">
            <For each={order.tags}>
              {(tag) => (
                <span class="px-golden-xs py-0.5 bg-white/10 rounded text-white/70 text-xs">
                  {tag}
                </span>
              )}
            </For>
          </div>

          {/* Status */}
          <div class="flex items-center justify-between pt-golden-sm border-t border-white/10">
            <div class="flex items-center space-x-golden-xs">
              <StatusIcon size={14} class="text-white/70" />
              <span class="text-white/70 text-sm capitalize">{order.status.replace('-', ' ')}</span>
            </div>
            <span class="text-white/50 text-xs capitalize">{order.priority}</span>
          </div>
        </div>
      </CosmicCard>
    )
  }

  return (
    <div class="space-y-golden-lg">
      {/* Header */}
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div>
          <h2 class="text-2xl font-bold text-white mb-golden-sm">Service Order Board</h2>
          <p class="text-white/70">Kanban-style service order management</p>
        </div>
        
        <GoldenButton variant="divine" size="lg" glow physics>
          <Plus size={20} class="mr-golden-sm" />
          Create Order
        </GoldenButton>
      </div>

      {/* Filters */}
      <div
        class={`transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="md" glow>
          <div class="flex flex-col lg:flex-row lg:items-center space-y-golden-md lg:space-y-0 lg:space-x-golden-md">
            {/* Search */}
            <div class="flex-1 relative">
              <Search size={20} class="absolute left-golden-sm top-1/2 transform -translate-y-1/2 text-white/50" />
              <input
                type="text"
                placeholder="Search orders..."
                class="w-full bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg pl-10 pr-golden-md py-golden-sm text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                value={searchTerm()}
                onInput={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Status Filter */}
            <div class="flex items-center space-x-golden-sm">
              <Filter size={16} class="text-white/70" />
              <select
                class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg px-golden-md py-golden-sm text-white focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                value={selectedStatus()}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <option value="all">All Status</option>
                <For each={STATUSES}>
                  {(status) => (
                    <option value={status.key} class="bg-gray-800 text-white">
                      {status.label}
                    </option>
                  )}
                </For>
              </select>
            </div>
          </div>
        </CosmicCard>
      </div>

      {/* Kanban Board */}
      <div
        class={`transition-all duration-1000 delay-400 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md">
          <For each={STATUSES}>
            {(status) => {
              const StatusIcon = status.icon
              const statusOrders = getOrdersByStatus(status.key)
              
              return (
                <div class="space-y-golden-md">
                  {/* Column Header */}
                  <CosmicCard variant="cosmic" size="sm" glow>
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-golden-sm">
                        <div class={`w-3 h-3 rounded-full ${status.color}`} />
                        <span class="font-bold text-white">{status.label}</span>
                      </div>
                      <span class="bg-white/20 text-white text-xs px-golden-xs py-0.5 rounded-full">
                        {statusOrders.length}
                      </span>
                    </div>
                  </CosmicCard>

                  {/* Orders */}
                  <div class="space-y-golden-sm min-h-[400px]">
                    <For each={statusOrders}>
                      {(order) => <OrderCard order={order} />}
                    </For>
                    
                    {statusOrders.length === 0 && (
                      <div class="text-center py-golden-xl text-white/50">
                        <StatusIcon size={32} class="mx-auto mb-golden-sm opacity-30" />
                        <p class="text-sm">No orders in {status.label.toLowerCase()}</p>
                      </div>
                    )}
                  </div>
                </div>
              )
            }}
          </For>
        </div>
      </div>
    </div>
  )
}
