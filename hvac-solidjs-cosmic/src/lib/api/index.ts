// 🌌 COSMIC API - Complete Integration Export
// One-stop shop for all API functionality

// Core API client and configuration
export { default as cosmic<PERSON><PERSON>, AuthManager, cosmicConfig, healthCheck } from './client'
export { CosmicAPIError } from './client'

// Type definitions
export type * from './types'

// Reactive hooks for SolidJS
export * from './hooks'

// 🚀 Quick Start API Services
import { cosmicAPI } from './client'
import type { 
  Customer, 
  CreateCustomerInput, 
  UpdateCustomerInput,
  ServiceJob,
  CreateJobInput,
  SearchFilters,
  PaginationParams
} from './types'

// 🏢 Customer Service
export const customerService = {
  // List customers with optional filtering and pagination
  list: (filters?: SearchFilters, pagination?: PaginationParams) =>
    cosmicAPI.customer.list.query({ filters, pagination }),
  
  // Get single customer by ID
  get: (id: string) =>
    cosmicAPI.customer.get.query({ id }),
  
  // Create new customer
  create: (input: CreateCustomerInput) =>
    cosmicAPI.customer.create.mutate(input),
  
  // Update existing customer
  update: (input: UpdateCustomerInput) =>
    cosmicAPI.customer.update.mutate(input),
  
  // Delete customer
  delete: (id: string) =>
    cosmicAPI.customer.delete.mutate({ id }),
}

// 🔧 Job Service
export const jobService = {
  // List jobs with optional filtering and pagination
  list: (filters?: SearchFilters, pagination?: PaginationParams) =>
    cosmicAPI.job.list.query({ filters, pagination }),
  
  // Get single job by ID
  get: (id: string) =>
    cosmicAPI.job.get.query({ id }),
  
  // Create new job
  create: (input: CreateJobInput) =>
    cosmicAPI.job.create.mutate(input),
  
  // Update existing job
  update: (input: Partial<ServiceJob> & { id: string }) =>
    cosmicAPI.job.update.mutate(input),
  
  // Delete job
  delete: (id: string) =>
    cosmicAPI.job.delete.mutate({ id }),
}

// 📊 Analytics Service
export const analyticsService = {
  // Get dashboard metrics
  getDashboard: () =>
    cosmicAPI.analytics.dashboard.query(),
  
  // Get revenue data for date range
  getRevenue: (dateFrom: string, dateTo: string) =>
    cosmicAPI.analytics.revenue.query({ dateFrom, dateTo }),
}

// 🧠 AI Service
export const aiService = {
  // Analyze email content
  analyzeEmail: (emailId: string) =>
    cosmicAPI.ai.analyzeEmail.mutate({ emailId }),
  
  // Analyze arbitrary text
  analyzeText: (text: string, context?: string) =>
    cosmicAPI.ai.analyzeText.mutate({ text, context }),
}

// 📧 Email Service
export const emailService = {
  // List emails with optional filtering
  list: (filters?: SearchFilters, pagination?: PaginationParams) =>
    cosmicAPI.email.list.query({ filters, pagination }),
  
  // Process email for AI analysis
  process: (emailId: string) =>
    cosmicAPI.email.process.mutate({ emailId }),
}

// 🔄 Workflow Service
export const workflowService = {
  // List all workflows
  list: () =>
    cosmicAPI.workflow.list.query(),
  
  // Get single workflow
  get: (id: string) =>
    cosmicAPI.workflow.get.query({ id }),
  
  // Create new workflow
  create: (input: Omit<import('./types').Workflow, 'id' | 'createdAt' | 'updatedAt'>) =>
    cosmicAPI.workflow.create.mutate(input),
  
  // Update workflow step
  updateStep: (workflowId: string, stepId: string, status: import('./types').WorkflowStep['status']) =>
    cosmicAPI.workflow.updateStep.mutate({ workflowId, stepId, status }),
}

// 🌟 Unified API Object
export const api = {
  customer: customerService,
  job: jobService,
  analytics: analyticsService,
  ai: aiService,
  email: emailService,
  workflow: workflowService,
}

// 🎯 Default export for convenience
export default api

// 🔧 Utility functions
export const apiUtils = {
  // Check if API is healthy
  async isHealthy(): Promise<boolean> {
    try {
      return await healthCheck.checkBackend()
    } catch {
      return false
    }
  },

  // Format error for display
  formatError(error: unknown): string {
    if (error instanceof CosmicAPIError) {
      return error.message
    }
    if (error instanceof Error) {
      return error.message
    }
    return 'An unexpected error occurred'
  },

  // Create search filters helper
  createFilters(params: {
    query?: string
    status?: string[]
    dateFrom?: string
    dateTo?: string
    tags?: string[]
    assignedTo?: string
  }): SearchFilters {
    return {
      query: params.query,
      status: params.status,
      dateFrom: params.dateFrom,
      dateTo: params.dateTo,
      tags: params.tags,
      assignedTo: params.assignedTo,
    }
  },

  // Create pagination helper
  createPagination(params: {
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }): PaginationParams {
    return {
      page: params.page || 1,
      limit: params.limit || 20,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder || 'desc',
    }
  },
}

// 🌌 Re-export everything for maximum flexibility
export * from './client'
export * from './types'
export * from './hooks'
