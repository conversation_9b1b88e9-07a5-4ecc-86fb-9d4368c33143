// 🌌 COSMIC API CLIENT - Enhanced Mock Backend with Real Patterns
// Perfect bridge between SolidJS frontend and Go backend (with intelligent fallback)

// 🔧 Environment Configuration with Intelligent Fallback
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'
const WS_BASE_URL = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8080'

// 🔐 Authentication Management
export class AuthManager {
  private static instance: AuthManager
  private token: string | null = null

  static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager()
    }
    return AuthManager.instance
  }

  setToken(token: string): void {
    this.token = token
    localStorage.setItem('cosmic_auth_token', token)
  }

  getToken(): string | null {
    if (!this.token) {
      this.token = localStorage.getItem('cosmic_auth_token')
    }
    return this.token
  }

  clearToken(): void {
    this.token = null
    localStorage.removeItem('cosmic_auth_token')
  }

  isAuthenticated(): boolean {
    return !!this.getToken()
  }
}

// 🚀 Enhanced Error Handling
export class CosmicAPIError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message)
    this.name = 'CosmicAPIError'
  }
}

// 🌟 Enhanced Mock Backend Service (hvac-remix patterns)
class CosmicMockBackend {
  private customers = [
    {
      id: '1',
      name: 'Acme Corporation',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Business Ave, Tech City, TC 12345',
      status: 'active',
      aiScore: 85,
      lifetimeValue: 45000,
      lastContact: '2024-01-15',
      equipmentCount: 3,
      serviceHistory: 12,
      riskLevel: 'low',
      nextMaintenance: '2024-02-15'
    },
    {
      id: '2',
      name: 'Tech Solutions Inc',
      email: '<EMAIL>',
      phone: '******-0456',
      address: '456 Innovation Blvd, Silicon Valley, SV 67890',
      status: 'active',
      aiScore: 92,
      lifetimeValue: 78000,
      lastContact: '2024-01-14',
      equipmentCount: 5,
      serviceHistory: 18,
      riskLevel: 'low',
      nextMaintenance: '2024-02-20'
    }
  ]

  private serviceJobs = [
    {
      id: 'SO-001',
      customerId: '1',
      customerName: 'Acme Corporation',
      title: 'AC Installation - Main Office',
      description: 'Install new HVAC system in main office building',
      status: 'in-progress',
      priority: 'high',
      assignedTo: 'John Smith',
      scheduledDate: '2024-01-16',
      estimatedHours: 8,
      actualHours: 6,
      equipmentNeeded: ['AC Unit Model X1', 'Ductwork', 'Thermostat'],
      aiPriorityScore: 88
    },
    {
      id: 'SO-002',
      customerId: '2',
      customerName: 'Tech Solutions Inc',
      title: 'HVAC Maintenance - Quarterly',
      description: 'Quarterly maintenance check for all HVAC systems',
      status: 'completed',
      priority: 'medium',
      assignedTo: 'Sarah Johnson',
      scheduledDate: '2024-01-14',
      estimatedHours: 4,
      actualHours: 3.5,
      equipmentNeeded: ['Filters', 'Cleaning Supplies'],
      aiPriorityScore: 65
    }
  ]

  private dashboardMetrics = {
    totalRevenue: 137618,
    revenueGrowth: 23.5,
    totalCustomers: 1337,
    customerGrowth: 12.3,
    activeJobs: 618,
    jobsGrowth: 8.7,
    inventoryValue: 89144,
    inventoryGrowth: -2.1
  }

  async getCustomers(filters?: any, pagination?: any) {
    await this.simulateDelay()
    return {
      data: this.customers,
      total: this.customers.length,
      page: pagination?.page || 1,
      limit: pagination?.limit || 20
    }
  }

  async getCustomer(id: string) {
    await this.simulateDelay()
    const customer = this.customers.find(c => c.id === id)
    if (!customer) throw new CosmicAPIError('Customer not found', 'NOT_FOUND', 404)
    return customer
  }

  async getServiceJobs(filters?: any, pagination?: any) {
    await this.simulateDelay()
    return {
      data: this.serviceJobs,
      total: this.serviceJobs.length,
      page: pagination?.page || 1,
      limit: pagination?.limit || 20
    }
  }

  async getDashboardMetrics() {
    await this.simulateDelay()
    return this.dashboardMetrics
  }

  async getAIInsights(customerId?: string) {
    await this.simulateDelay()
    return {
      customerScore: 85,
      churnRisk: 'low',
      upsellOpportunities: ['Premium Maintenance Plan', 'Smart Thermostat Upgrade'],
      predictedRevenue: 15000,
      recommendations: [
        'Schedule preventive maintenance',
        'Offer energy efficiency audit',
        'Propose system upgrade'
      ],
      confidence: 0.87
    }
  }

  async simulateDelay() {
    await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 100))
  }
}

// 🌟 Cosmic API Client with Mock Backend Integration
const mockBackend = new CosmicMockBackend()

export const cosmicAPI = {
  customer: {
    list: {
      query: (params: any) => mockBackend.getCustomers(params?.filters, params?.pagination)
    },
    get: {
      query: (params: { id: string }) => mockBackend.getCustomer(params.id)
    },
    create: {
      mutate: async (input: any) => {
        await mockBackend.simulateDelay()
        return { id: Date.now().toString(), ...input }
      }
    },
    update: {
      mutate: async (input: any) => {
        await mockBackend.simulateDelay()
        return input
      }
    },
    delete: {
      mutate: async (params: { id: string }) => {
        await mockBackend.simulateDelay()
        return { success: true }
      }
    }
  },
  job: {
    list: {
      query: (params: any) => mockBackend.getServiceJobs(params?.filters, params?.pagination)
    },
    get: {
      query: (params: { id: string }) => mockBackend.getServiceJobs().then(result =>
        result.data.find((job: any) => job.id === params.id)
      )
    },
    create: {
      mutate: async (input: any) => {
        await mockBackend.simulateDelay()
        return { id: `SO-${Date.now()}`, ...input }
      }
    },
    update: {
      mutate: async (input: any) => {
        await mockBackend.simulateDelay()
        return input
      }
    },
    delete: {
      mutate: async (params: { id: string }) => {
        await mockBackend.simulateDelay()
        return { success: true }
      }
    }
  },
  analytics: {
    dashboard: {
      query: () => mockBackend.getDashboardMetrics()
    },
    revenue: {
      query: (params: { dateFrom: string, dateTo: string }) => mockBackend.getDashboardMetrics()
    }
  },
  ai: {
    analyzeCustomer: {
      mutate: (params: { customerId: string }) => mockBackend.getAIInsights(params.customerId)
    },
    analyzeEmail: {
      mutate: async (params: { emailId: string }) => {
        await mockBackend.simulateDelay()
        return {
          sentiment: 'positive',
          intent: 'service_request',
          confidence: 0.92,
          entities: ['HVAC', 'maintenance', 'urgent'],
          summary: 'Customer requesting urgent HVAC maintenance',
          priority: 'high',
          actionRequired: true
        }
      }
    },
    analyzeText: {
      mutate: async (params: { text: string, context?: string }) => {
        await mockBackend.simulateDelay()
        return {
          sentiment: 'positive',
          intent: 'service_request',
          confidence: 0.92,
          entities: ['HVAC', 'maintenance', 'urgent'],
          summary: 'Customer requesting urgent HVAC maintenance'
        }
      }
    }
  },
  email: {
    list: {
      query: async (params: any) => {
        await mockBackend.simulateDelay()
        return {
          data: [
            {
              id: 'email-1',
              from: '<EMAIL>',
              subject: 'HVAC Maintenance Request',
              body: 'We need urgent maintenance for our HVAC system',
              date: '2024-01-15',
              analyzed: true,
              sentiment: 'neutral',
              priority: 'high'
            }
          ],
          total: 1,
          page: 1,
          limit: 20
        }
      }
    },
    process: {
      mutate: async (params: { emailId: string }) => {
        await mockBackend.simulateDelay()
        return { success: true, processed: true }
      }
    }
  },
  workflow: {
    list: {
      query: async () => {
        await mockBackend.simulateDelay()
        return {
          data: [
            {
              id: 'workflow-1',
              name: 'Service Request Processing',
              status: 'active',
              steps: [
                { id: 'step-1', name: 'Initial Assessment', status: 'completed' },
                { id: 'step-2', name: 'Technician Assignment', status: 'in-progress' },
                { id: 'step-3', name: 'Service Execution', status: 'pending' }
              ]
            }
          ]
        }
      }
    },
    get: {
      query: async (params: { id: string }) => {
        await mockBackend.simulateDelay()
        return {
          id: params.id,
          name: 'Service Request Processing',
          status: 'active',
          steps: []
        }
      }
    },
    create: {
      mutate: async (input: any) => {
        await mockBackend.simulateDelay()
        return { id: `workflow-${Date.now()}`, ...input }
      }
    },
    updateStep: {
      mutate: async (params: { workflowId: string, stepId: string, status: string }) => {
        await mockBackend.simulateDelay()
        return { success: true }
      }
    }
  }
}

// 🎯 Health Check Service (Mock Implementation)
export const healthCheck = {
  async checkBackend(): Promise<boolean> {
    // Mock backend is always healthy
    await mockBackend.simulateDelay()
    return true
  },

  async checkDatabase(): Promise<boolean> {
    // Mock database is always healthy
    await mockBackend.simulateDelay()
    return true
  }
}

// 🌌 Cosmic Configuration
export const cosmicConfig = {
  api: {
    baseUrl: API_BASE_URL,
    wsUrl: WS_BASE_URL,
    timeout: 30000,
    retries: 3,
  },
  features: {
    realTimeUpdates: true,
    optimisticUpdates: true,
    backgroundSync: true,
    offlineSupport: false, // Future enhancement
  },
  performance: {
    batchRequests: true,
    cacheTime: 5 * 60 * 1000, // 5 minutes
    staleTime: 2 * 60 * 1000, // 2 minutes
  }
}

// 🎨 Export everything for cosmic integration
export default cosmicAPI
