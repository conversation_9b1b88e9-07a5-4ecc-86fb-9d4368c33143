// 🌌 COSMIC API CLIENT - Type-Safe GoBackend-Kratos Integration
// Perfect bridge between SolidJS frontend and Go backend

import { createTRPCProxyClient, httpBatchLink, wsLink, splitLink } from '@trpc/client'
import type { AppRouter, TRPCClient } from './types'

// 🔧 Environment Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'
const WS_BASE_URL = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8080'

// 🔐 Authentication Management
export class AuthManager {
  private static instance: AuthManager
  private token: string | null = null

  static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager()
    }
    return AuthManager.instance
  }

  setToken(token: string): void {
    this.token = token
    localStorage.setItem('cosmic_auth_token', token)
  }

  getToken(): string | null {
    if (!this.token) {
      this.token = localStorage.getItem('cosmic_auth_token')
    }
    return this.token
  }

  clearToken(): void {
    this.token = null
    localStorage.removeItem('cosmic_auth_token')
  }

  isAuthenticated(): boolean {
    return !!this.getToken()
  }
}

// 🚀 Enhanced Error Handling
export class CosmicAPIError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message)
    this.name = 'CosmicAPIError'
  }
}

// 🌟 Cosmic tRPC Client with Advanced Features
export const cosmicAPI = createTRPCProxyClient<AppRouter>({
  links: [
    splitLink({
      condition: (op) => op.type === 'subscription',
      true: wsLink({
        url: `${WS_BASE_URL}/api/trpc`,
        connectionParams: () => {
          const auth = AuthManager.getInstance()
          return {
            authorization: auth.getToken() ? `Bearer ${auth.getToken()}` : undefined,
          }
        },
        retryDelayMs: (attempt) => Math.min(1000 * 2 ** attempt, 30000),
      }),
      false: httpBatchLink({
        url: `${API_BASE_URL}/api/trpc`,
        headers: () => {
          const auth = AuthManager.getInstance()
          const headers: Record<string, string> = {
            'Content-Type': 'application/json',
          }
          
          if (auth.getToken()) {
            headers.authorization = `Bearer ${auth.getToken()}`
          }
          
          return headers
        },
        fetch: async (url, options) => {
          try {
            const response = await fetch(url, {
              ...options,
              timeout: 30000, // 30 second timeout
            })

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({}))
              throw new CosmicAPIError(
                errorData.message || `API Error: ${response.status}`,
                errorData.code || 'API_ERROR',
                response.status,
                errorData
              )
            }

            return response
          } catch (error) {
            if (error instanceof CosmicAPIError) {
              throw error
            }
            throw new CosmicAPIError(
              'Network error occurred',
              'NETWORK_ERROR',
              undefined,
              error
            )
          }
        },
      }),
    }),
  ],
})

// 🎯 Health Check Service
export const healthCheck = {
  async checkBackend(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/health`)
      return response.ok
    } catch {
      return false
    }
  },

  async checkDatabase(): Promise<boolean> {
    try {
      // This would call a specific health endpoint for DB
      const response = await fetch(`${API_BASE_URL}/health/db`)
      return response.ok
    } catch {
      return false
    }
  }
}

// 🌌 Cosmic Configuration
export const cosmicConfig = {
  api: {
    baseUrl: API_BASE_URL,
    wsUrl: WS_BASE_URL,
    timeout: 30000,
    retries: 3,
  },
  features: {
    realTimeUpdates: true,
    optimisticUpdates: true,
    backgroundSync: true,
    offlineSupport: false, // Future enhancement
  },
  performance: {
    batchRequests: true,
    cacheTime: 5 * 60 * 1000, // 5 minutes
    staleTime: 2 * 60 * 1000, // 2 minutes
  }
}

// 🎨 Export everything for cosmic integration
export type { AppRouter } from './types'
export default cosmicAPI
