{"version": 3, "file": "TRPCClientError.d.ts", "sourceRoot": "", "sources": ["../src/TRPCClientError.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,gBAAgB,EAChB,qBAAqB,EACrB,KAAK,EACL,iBAAiB,EAClB,MAAM,0CAA0C,CAAC;AAClD,OAAO,EAEL,KAAK,iBAAiB,EACvB,MAAM,0CAA0C,CAAC;AAElD,KAAK,eAAe,CAAC,WAAW,SAAS,qBAAqB,IAC5D,gBAAgB,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC;AAC9C,MAAM,WAAW,mBAAmB,CAAC,MAAM,SAAS,iBAAiB;IACnE,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;IACzB,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAC9B,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;CACtC;AACD,MAAM,MAAM,mBAAmB,CAAC,WAAW,SAAS,qBAAqB,IACvE,mBAAmB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC;AAgCpD,qBAAa,eAAe,CAAC,kBAAkB,SAAS,qBAAqB,CAC3E,SAAQ,KACR,YAAW,mBAAmB,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;IAInE,SAAyB,KAAK,oBAAC;IAC/B,SAAgB,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAClE,SAAgB,IAAI,EAAE,KAAK,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IAEzE;;;OAGG;IACI,IAAI,sCAAC;gBAGV,OAAO,EAAE,MAAM,EACf,IAAI,CAAC,EAAE;QACL,MAAM,CAAC,EAAE,KAAK,CAAC,iBAAiB,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACvE,KAAK,CAAC,EAAE,KAAK,CAAC;QACd,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KAChC;WAkBW,IAAI,CAAC,kBAAkB,SAAS,qBAAqB,EACjE,MAAM,EAAE,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,MAAM,EAC/C,IAAI,GAAE;QAAE,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;KAAO,GAC5C,eAAe,CAAC,kBAAkB,CAAC;CA2BvC"}