import type { AnyRouter, InferrableClientTypes } from '@trpc/server/unstable-core-do-not-import';
import type { TRPCClientError } from '../TRPCClientError';
import type { Operation, OperationResultEnvelope, TRPCLink } from './types';
type ConsoleEsque = {
    log: (...args: any[]) => void;
    error: (...args: any[]) => void;
};
type EnableFnOptions<TRouter extends InferrableClientTypes> = {
    direction: 'down';
    result: OperationResultEnvelope<unknown, TRPCClientError<TRouter>> | TRPCClientError<TRouter>;
} | (Operation & {
    direction: 'up';
});
type EnabledFn<TRouter extends AnyRouter> = (opts: EnableFnOptions<TRouter>) => boolean;
type LoggerLinkFnOptions<TRouter extends AnyRouter> = Operation & ({
    /**
     * Request result
     */
    direction: 'down';
    result: OperationResultEnvelope<unknown, TRPCClientError<TRouter>> | TRPCClientError<TRouter>;
    elapsedMs: number;
} | {
    /**
     * Request was just initialized
     */
    direction: 'up';
});
type LoggerLinkFn<TRouter extends AnyRouter> = (opts: LoggerLinkFnOptions<TRouter>) => void;
type ColorMode = 'ansi' | 'css' | 'none';
export interface LoggerLinkOptions<TRouter extends AnyRouter> {
    logger?: LoggerLinkFn<TRouter>;
    enabled?: EnabledFn<TRouter>;
    /**
     * Used in the built-in defaultLogger
     */
    console?: ConsoleEsque;
    /**
     * Color mode
     * @default typeof window === 'undefined' ? 'ansi' : 'css'
     */
    colorMode?: ColorMode;
    /**
     * Include context in the log - defaults to false unless `colorMode` is 'css'
     */
    withContext?: boolean;
}
/**
 * @see https://trpc.io/docs/v11/client/links/loggerLink
 */
export declare function loggerLink<TRouter extends AnyRouter = AnyRouter>(opts?: LoggerLinkOptions<TRouter>): TRPCLink<TRouter>;
export {};
//# sourceMappingURL=loggerLink.d.ts.map