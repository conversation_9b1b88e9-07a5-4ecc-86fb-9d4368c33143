{"version": 3, "file": "wsClient.d.ts", "sourceRoot": "", "sources": ["../../../../src/links/wsLink/wsClient/wsClient.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAClD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAE/D,OAAO,KAAK,EACV,uBAAuB,EAKxB,MAAM,0CAA0C,CAAC;AAMlD,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,+BAA+B,CAAC;AACzE,OAAO,KAAK,EAAE,SAAS,EAAE,uBAAuB,EAAE,MAAM,aAAa,CAAC;AACtE,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,WAAW,CAAC;AAOxD;;;GAGG;AACH,qBAAa,QAAQ;IACnB;;OAEG;IACH,SAAgB,eAAe,EAAE,eAAe,CAC9C,mBAAmB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CACpD,CAAC;IAEF,OAAO,CAAC,cAAc,CAAS;IAC/B,OAAO,CAAC,cAAc,CAAwB;IAC9C,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAe;IAChD,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAmC;IACvE,OAAO,CAAC,iBAAiB,CAAoB;IAC7C,OAAO,CAAC,QAAQ,CAAC,SAAS,CAGxB;IACF,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAU;gBAEvB,IAAI,EAAE,sBAAsB;IA2DxC;;;OAGG;YACW,IAAI;IAuBlB;;;OAGG;IACU,KAAK;IA+BlB;;;;;;;;;OASG;IACI,OAAO,CAAC,EACb,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,EACrC,WAAW,EACX,WAAW,GACZ,EAAE;QACD,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,QAAQ,CAAC,CAAC;QACjE,WAAW,EAAE,uBAAuB,CAAC;QACrC,WAAW,CAAC,EAAE,MAAM,CAAC;KACtB;IA+CD,IAAW,UAAU;;;;;;;;;;;;aAEpB;IAED;;;;OAIG;IACH,OAAO,CAAC,YAAY,CAA8B;IAClD,OAAO,CAAC,SAAS;IAgCjB,OAAO,CAAC,uBAAuB;IA+E/B,OAAO,CAAC,qBAAqB;IAuB7B,OAAO,CAAC,qBAAqB;IAU7B;;OAEG;IACH,OAAO,CAAC,IAAI;IAgBZ;;;;OAIG;IACH,OAAO,CAAC,SAAS;CAmBlB"}