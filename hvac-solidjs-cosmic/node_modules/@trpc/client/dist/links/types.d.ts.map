{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../src/links/types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACpE,OAAO,KAAK,EACV,qBAAqB,EACrB,KAAK,EACL,iBAAiB,EACjB,mBAAmB,EACpB,MAAM,0CAA0C,CAAC;AAClD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACxD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAC1D,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AAErE,OAAO,EACL,qBAAqB,EACrB,UAAU,EACV,WAAW,GACZ,MAAM,0BAA0B,CAAC;AAElC;;GAEG;AACH,MAAM,WAAW,gBAAiB,SAAQ,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;CAAG;AAEpE;;GAEG;AACH,MAAM,MAAM,SAAS,CAAC,MAAM,GAAG,OAAO,IAAI;IACxC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,UAAU,GAAG,OAAO,GAAG,cAAc,CAAC;IAC5C,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,gBAAgB,CAAC;IAC1B,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;CAC5B,CAAC;AAEF,UAAU,gBAAgB;IACxB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,gBAAgB,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;CACzD;AAED;;GAEG;AACH,MAAM,MAAM,WAAW,GACnB,gBAAgB,GAChB,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,SAAS,CAAC,CAAC;AAElD;;;GAGG;AACH,MAAM,MAAM,SAAS,GAAG,CACtB,GAAG,EAAE,MAAM,EACX,OAAO,CAAC,EAAE,WAAW,KAClB,OAAO,CAAC,aAAa,CAAC,CAAC;AAE5B,MAAM,WAAW,iBAAiB;CAEjC;AAED;;GAEG;AACH,MAAM,WAAW,uBAAuB,CAAC,OAAO,EAAE,MAAM;IACtD,MAAM,EACF,iBAAiB,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,GACpC,mBAAmB,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,GACtC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAChC,OAAO,CAAC,EAAE,gBAAgB,CAAC;CAC5B;AAED;;GAEG;AACH,MAAM,MAAM,yBAAyB,CACnC,WAAW,SAAS,qBAAqB,EACzC,OAAO,IACL,UAAU,CACZ,uBAAuB,CAAC,OAAO,EAAE,eAAe,CAAC,WAAW,CAAC,CAAC,EAC9D,eAAe,CAAC,WAAW,CAAC,CAC7B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,uBAAuB,CACjC,WAAW,SAAS,qBAAqB,EACzC,OAAO,IACL,QAAQ,CACV,uBAAuB,CAAC,OAAO,EAAE,eAAe,CAAC,WAAW,CAAC,CAAC,EAC9D,eAAe,CAAC,WAAW,CAAC,CAC7B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,aAAa,CACvB,WAAW,SAAS,qBAAqB,EACzC,MAAM,GAAG,OAAO,EAChB,OAAO,GAAG,OAAO,IACf,CAAC,IAAI,EAAE;IACT,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;IACtB,IAAI,EAAE,CACJ,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,KAClB,yBAAyB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;CACtD,KAAK,yBAAyB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAEtD;;GAEG;AACH,MAAM,MAAM,QAAQ,CAAC,WAAW,SAAS,qBAAqB,IAAI,CAChE,IAAI,EAAE,iBAAiB,KACpB,aAAa,CAAC,WAAW,CAAC,CAAC"}