{"version": 3, "file": "createTRPCClient.d.ts", "sourceRoot": "", "sources": ["../src/createTRPCClient.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAC9D,OAAO,KAAK,EACV,YAAY,EACZ,SAAS,EACT,gBAAgB,EAChB,mBAAmB,EACnB,qBAAqB,EACrB,+BAA+B,EAC/B,aAAa,EACb,YAAY,EACb,MAAM,0CAA0C,CAAC;AAKlD,OAAO,KAAK,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACzE,OAAO,KAAK,EAAE,wBAAwB,EAAE,MAAM,+BAA+B,CAAC;AAC9E,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEzD;;;IAGI;AACJ,MAAM,MAAM,iBAAiB,CAAC,OAAO,SAAS,SAAS,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;AAE/E;;;IAGI;AACJ,MAAM,MAAM,gBAAgB,CAAC,OAAO,SAAS,SAAS,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;AAE9E,QAAA,MAAM,mBAAmB,eAAmC,CAAC;AAE7D;;IAEI;AACJ,MAAM,MAAM,UAAU,CAAC,OAAO,SAAS,SAAS,IAAI,wBAAwB,CAC1E;IACE,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IACjE,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;CAChE,EACD,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAC1B,GAAG;IACF,CAAC,mBAAmB,CAAC,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;CACnD,CAAC;AAEF,KAAK,WAAW,GAAG;IACjB,KAAK,EAAE,GAAG,CAAC;IACX,MAAM,EAAE,GAAG,CAAC;IACZ,WAAW,EAAE,OAAO,CAAC;IACrB,UAAU,EAAE,GAAG,CAAC;CACjB,CAAC;AAEF,KAAK,8BAA8B,CAAC,CAAC,IACnC,CAAC,SAAS,cAAc,CAAC,MAAM,EAAE,EAAE,MAAM,OAAO,EAAE,MAAM,KAAK,CAAC,GAC1D,aAAa,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,GACjC,CAAC,CAAC;AAER,gBAAgB;AAChB,MAAM,MAAM,QAAQ,CAAC,IAAI,SAAS,WAAW,IAAI,CAC/C,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,EACpB,IAAI,CAAC,EAAE,oBAAoB,KACxB,OAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAE7D,KAAK,oBAAoB,CAAC,IAAI,SAAS,WAAW,IAAI,CACpD,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,EACpB,IAAI,EAAE,OAAO,CACX,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC,CAChE,GACC,oBAAoB,KACnB,cAAc,CAAC;AAEpB,KAAK,iBAAiB,CACpB,KAAK,SAAS,aAAa,EAC3B,IAAI,SAAS,WAAW,IACtB,KAAK,SAAS,OAAO,GACrB;IACE,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;CACvB,GACD,KAAK,SAAS,UAAU,GACtB;IACE,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;CACxB,GACD,KAAK,SAAS,cAAc,GAC1B;IACE,SAAS,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;CACvC,GACD,KAAK,CAAC;AAEd;;GAEG;AACH,KAAK,wBAAwB,CAC3B,KAAK,SAAS,qBAAqB,EACnC,OAAO,SAAS,YAAY,IAC1B;KACD,IAAI,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,MAAM,MAAM,GACvD,MAAM,SAAS,YAAY,GACzB,iBAAiB,CACf,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,EACtB;QACE,KAAK,EAAE,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACnC,MAAM,EAAE,+BAA+B,CACrC,gBAAgB,CAAC,KAAK,CAAC,EACvB,MAAM,CACP,CAAC;QACF,UAAU,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC;QAClD,WAAW,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC;KACrD,CACF,GACD,MAAM,SAAS,YAAY,GACzB,wBAAwB,CAAC,KAAK,EAAE,MAAM,CAAC,GACvC,KAAK,GACT,KAAK;CACV,CAAC;AAWF,gBAAgB;AAChB,eAAO,MAAM,6BAA6B,GACxC,gBAAgB,MAAM,KACrB,aAEF,CAAC;AAEF;;GAEG;AACH,wBAAgB,qBAAqB,CAAC,OAAO,SAAS,SAAS,EAC7D,MAAM,EAAE,iBAAiB,CAAC,OAAO,CAAC,GACjC,UAAU,CAAC,OAAO,CAAC,CAerB;AAED,wBAAgB,gBAAgB,CAAC,OAAO,SAAS,SAAS,EACxD,IAAI,EAAE,uBAAuB,CAAC,OAAO,CAAC,GACrC,UAAU,CAAC,OAAO,CAAC,CAIrB;AAED;;;GAGG;AACH,wBAAgB,gBAAgB,CAAC,OAAO,SAAS,SAAS,EACxD,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,GAC1B,iBAAiB,CAAC,OAAO,CAAC,CAE5B"}