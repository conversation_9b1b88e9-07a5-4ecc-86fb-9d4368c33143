{"version": 3, "file": "TRPCUntypedClient.d.ts", "sourceRoot": "", "sources": ["../../src/internals/TRPCUntypedClient.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAEV,cAAc,EACf,MAAM,yBAAyB,CAAC;AAEjC,OAAO,KAAK,EACV,SAAS,EACT,uBAAuB,EACvB,qBAAqB,EAErB,SAAS,EACV,MAAM,0CAA0C,CAAC;AAElD,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAC5E,OAAO,KAAK,EACV,gBAAgB,EAEhB,iBAAiB,EACjB,QAAQ,EACT,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAGrD,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,OAAO,CAAC,EAAE,gBAAgB,CAAC;IAC3B,MAAM,CAAC,EAAE,WAAW,CAAC;CACtB;AAED,MAAM,WAAW,wBAAwB,CAAC,MAAM,EAAE,MAAM;IACtD,SAAS,EAAE,CAAC,IAAI,EAAE;QAAE,OAAO,EAAE,gBAAgB,GAAG,SAAS,CAAA;KAAE,KAAK,IAAI,CAAC;IACrE,MAAM,EAAE,CAAC,KAAK,EAAE,uBAAuB,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;IACzD,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;IAC/B,SAAS,EAAE,MAAM,IAAI,CAAC;IACtB,UAAU,EAAE,MAAM,IAAI,CAAC;IACvB,uBAAuB,EAAE,CAAC,KAAK,EAAE,mBAAmB,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;CACvE;AAED,gBAAgB;AAChB,MAAM,MAAM,uBAAuB,CAAC,OAAO,SAAS,qBAAqB,IAAI;IAC3E,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;IAC3B,WAAW,CAAC,EAAE,SAAS,CAAC,qEAAqE,CAAC,CAAC;CAChG,CAAC;AAEF,qBAAa,iBAAiB,CAAC,WAAW,SAAS,qBAAqB;IACtE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAA+B;IACrD,SAAgB,OAAO,EAAE,iBAAiB,CAAC;IAC3C,OAAO,CAAC,SAAS,CAAS;gBAEd,IAAI,EAAE,uBAAuB,CAAC,WAAW,CAAC;IAStD,OAAO,CAAC,QAAQ;YAkBF,gBAAgB;IAkBvB,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,kBAAkB;IAS9D,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,kBAAkB;IASjE,YAAY,CACjB,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,OAAO,EACd,IAAI,EAAE,OAAO,CACX,wBAAwB,CAAC,OAAO,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC,CAC9D,GACC,kBAAkB,GACnB,cAAc;CAwClB"}