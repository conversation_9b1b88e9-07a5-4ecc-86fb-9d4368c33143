{"name": "@trpc/server", "version": "11.1.3", "description": "The tRPC server library", "author": "KATT", "license": "MIT", "main": "dist/index.js", "module": "dist/index.mjs", "typings": "dist/index.d.ts", "homepage": "https://trpc.io", "repository": {"type": "git", "url": "git+https://github.com/trpc/trpc.git", "directory": "packages/server"}, "scripts": {"build": "rollup --config rollup.config.ts --configPlugin rollup-plugin-swc3", "dev": "pnpm build --watch", "codegen-entrypoints": "tsx entrypoints.script.ts", "benchmark": "tsc --project tsconfig.benchmark.json", "lint": "eslint --cache src", "ts-watch": "tsc --watch"}, "exports": {"./package.json": "./package.json", ".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "default": "./dist/index.js"}, "./adapters/aws-lambda": {"import": "./dist/adapters/aws-lambda/index.mjs", "require": "./dist/adapters/aws-lambda/index.js", "default": "./dist/adapters/aws-lambda/index.js"}, "./adapters/express": {"import": "./dist/adapters/express.mjs", "require": "./dist/adapters/express.js", "default": "./dist/adapters/express.js"}, "./adapters/fastify": {"import": "./dist/adapters/fastify/index.mjs", "require": "./dist/adapters/fastify/index.js", "default": "./dist/adapters/fastify/index.js"}, "./adapters/fetch": {"import": "./dist/adapters/fetch/index.mjs", "require": "./dist/adapters/fetch/index.js", "default": "./dist/adapters/fetch/index.js"}, "./adapters/next-app-dir": {"import": "./dist/adapters/next-app-dir.mjs", "require": "./dist/adapters/next-app-dir.js", "default": "./dist/adapters/next-app-dir.js"}, "./adapters/next": {"import": "./dist/adapters/next.mjs", "require": "./dist/adapters/next.js", "default": "./dist/adapters/next.js"}, "./adapters/node-http": {"import": "./dist/adapters/node-http/index.mjs", "require": "./dist/adapters/node-http/index.js", "default": "./dist/adapters/node-http/index.js"}, "./adapters/standalone": {"import": "./dist/adapters/standalone.mjs", "require": "./dist/adapters/standalone.js", "default": "./dist/adapters/standalone.js"}, "./adapters/ws": {"import": "./dist/adapters/ws.mjs", "require": "./dist/adapters/ws.js", "default": "./dist/adapters/ws.js"}, "./http": {"import": "./dist/http.mjs", "require": "./dist/http.js", "default": "./dist/http.js"}, "./observable": {"import": "./dist/observable/index.mjs", "require": "./dist/observable/index.js", "default": "./dist/observable/index.js"}, "./rpc": {"import": "./dist/rpc.mjs", "require": "./dist/rpc.js", "default": "./dist/rpc.js"}, "./shared": {"import": "./dist/shared.mjs", "require": "./dist/shared.js", "default": "./dist/shared.js"}, "./unstable-core-do-not-import": {"import": "./dist/unstable-core-do-not-import.mjs", "require": "./dist/unstable-core-do-not-import.js", "default": "./dist/unstable-core-do-not-import.js"}}, "files": ["dist", "src", "README.md", "package.json", "adapters", "http", "observable", "rpc", "shared", "unstable-core-do-not-import", "!**/*.test.*", "!**/__tests__"], "publishConfig": {"access": "public"}, "devDependencies": {"@fastify/websocket": "^10.0.1", "@tanstack/react-query": "^5.74.7", "@types/aws-lambda": "^8.10.137", "@types/express": "^5.0.0", "@types/hash-sum": "^1.0.0", "@types/node": "^22.13.5", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@types/ws": "^8.2.0", "devalue": "^5.0.0", "eslint": "^9.26.0", "express": "^5.0.0", "fastify": "^4.16.0", "fastify-plugin": "^5.0.0", "hash-sum": "^2.0.0", "myzod": "^1.3.1", "next": "^15.3.1", "react": "^19.1.0", "react-dom": "^19.1.0", "rollup": "^4.34.8", "superjson": "^1.12.4", "superstruct": "^2.0.0", "tsx": "^4.19.3", "typescript": "^5.8.2", "valibot": "1.1.0", "ws": "^8.0.0", "yup": "^1.0.0", "zod": "^3.24.4"}, "funding": ["https://trpc.io/sponsor"], "peerDependencies": {"typescript": ">=5.7.2"}, "gitHead": "136b18fc55fece9b8f06d4a17780cff7f55cabf5"}