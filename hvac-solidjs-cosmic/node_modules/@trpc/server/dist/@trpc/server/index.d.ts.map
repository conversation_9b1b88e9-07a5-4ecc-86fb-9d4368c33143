{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../src/@trpc/server/index.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,SAAS;AACT;;GAEG;AACH,iCAAiC,EACjC,iCAAiC,IAAI,2BAA2B,EAChE,QAAQ,EAER,uBAAuB,EACvB,qBAAqB,EACrB,eAAe,IAAI,mBAAmB,EACtC,oBAAoB,IAAI,wBAAwB,EAChD,KAAK,mBAAmB,EACxB,KAAK,oBAAoB,EACzB,KAAK,oCAAoC,EACzC,KAAK,gBAAgB,EACrB,KAAK,iBAAiB,EACtB,KAAK,kBAAkB,EACvB,KAAK,kBAAkB,EACvB,KAAK,gBAAgB,IAAI,oBAAoB,EAC7C,KAAK,cAAc,IAAI,kBAAkB,EACzC,KAAK,+BAA+B,EACpC,KAAK,kCAAkC,EACvC,KAAK,YAAY,IAAI,gBAAgB,EACrC,KAAK,SAAS,IAAI,aAAa,EAC/B,KAAK,YAAY,IAAI,gBAAgB,EACrC,KAAK,qBAAqB,IAAI,yBAAyB,EACvD,KAAK,uBAAuB,IAAI,2BAA2B,EAC3D,KAAK,aAAa,IAAI,iBAAiB,EACvC,KAAK,oBAAoB,IAAI,wBAAwB,EACrD,KAAK,iBAAiB,IAAI,qBAAqB,EAC/C,KAAK,YAAY,IAAI,gBAAgB,EACrC,KAAK,wBAAwB,IAAI,4BAA4B,EAC7D,KAAK,qBAAqB,EAC1B,KAAK,iBAAiB,IAAI,qBAAqB,EAC/C,KAAK,cAAc,IAAI,kBAAkB,EACzC,KAAK,qBAAqB,IAAI,yBAAyB,EACvD,KAAK,WAAW,EAChB,qBAAqB;AACrB;;GAEG;AACH,GAAG,EACH,OAAO,EACP,KAAK,eAAe,EACpB,iBAAiB,EACjB,IAAI;AACJ;;GAEG;AACH,IAAI,IAAI,iBAAiB,EACzB,aAAa,IAAI,iBAAiB,GACnC,MAAM,mCAAmC,CAAC;AAE3C,YAAY;AACV;;GAEG;AACH,YAAY;AACZ;;GAEG;AACH,SAAS;AACT;;GAEG;AACH,qBAAqB;AACrB;;GAEG;AACH,uBAAuB;AAEvB;;GAEG;AACH,IAAI;AAEJ;;GAEG;AACH,WAAW;AACX;;GAEG;AACH,aAAa;AACb;;GAEG;AACH,oBAAoB;AAEpB;;GAEG;AACH,iBAAiB;AACjB;;GAEG;AACH,wBAAwB,GACzB,MAAM,mCAAmC,CAAC;AAE3C,OAAO;AACL;;GAEG;AACH,aAAa,GACd,MAAM,mCAAmC,CAAC;AAE3C;;;GAGG;AACH,MAAM,MAAM,oBAAoB,CAAC,SAAS,SAAS,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,IACxE,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC"}