{"version": 3, "file": "standalone.d.ts", "sourceRoot": "", "sources": ["../../src/adapters/standalone.ts"], "names": [], "mappings": "AAAA;;;;;;;;GAQG;AAEH,OAAO,IAAI,MAAM,MAAM,CAAC;AAExB,OAAO,KAAK,KAAK,KAAK,MAAM,OAAO,CAAC;AAEpC,OAAO,EAAE,KAAK,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAGjD,OAAO,KAAK,EACV,8BAA8B,EAC9B,sBAAsB,EACtB,eAAe,EACf,gBAAgB,EACjB,MAAM,aAAa,CAAC;AAOrB,KAAK,wBAAwB,CAC3B,OAAO,SAAS,SAAS,EACzB,QAAQ,SAAS,eAAe,EAChC,SAAS,SAAS,gBAAgB,IAChC,sBAAsB,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,GAAG;IACzD;;;;;;;OAOG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB,CAAC;AAGF,MAAM,MAAM,wBAAwB,CAAC,OAAO,SAAS,SAAS,IAC5D,wBAAwB,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AAE/E,MAAM,MAAM,wBAAwB,GAAG,8BAA8B,CACnE,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,cAAc,CACpB,CAAC;AAsCF;;GAEG;AACH,wBAAgB,iBAAiB,CAAC,OAAO,SAAS,SAAS,EACzD,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,GACtC,IAAI,CAAC,eAAe,CAEtB;AAED,wBAAgB,gBAAgB,CAAC,OAAO,SAAS,SAAS,EACxD,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,wEAGxC;AAGD,MAAM,MAAM,yBAAyB,CAAC,OAAO,SAAS,SAAS,IAC7D,wBAAwB,CACtB,OAAO,EACP,KAAK,CAAC,kBAAkB,EACxB,KAAK,CAAC,mBAAmB,CAC1B,CAAC;AAEJ,MAAM,MAAM,yBAAyB,GAAG,8BAA8B,CACpE,KAAK,CAAC,kBAAkB,EACxB,KAAK,CAAC,mBAAmB,CAC1B,CAAC;AAEF,wBAAgB,kBAAkB,CAAC,IAAI,EAAE,yBAAyB,CAAC,SAAS,CAAC,gGA1DvC,IAAI,CA4DzC"}