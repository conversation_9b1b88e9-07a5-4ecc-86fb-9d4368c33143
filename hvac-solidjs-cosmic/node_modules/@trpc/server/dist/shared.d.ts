export { 
/**
 * @deprecated `import from '@trpc/server'` instead
 */
type inferProcedureInput, 
/**
 * @deprecated `import from '@trpc/server'` instead
 */
type inferProcedureOutput, 
/**
 * @deprecated `import from '@trpc/server'` instead
 */
type inferTransformedProcedureOutput, 
/**
 * @deprecated `import from '@trpc/server'` instead
 */
type inferTransformedSubscriptionOutput, 
/**
 * @deprecated `import from '@trpc/server'` instead
 */
getErrorShape, 
/**
 * @deprecated `import { createTRPCFlatProxy } from '@trpc/server'` instead
 */
createTRPCFlatProxy as createFlatProxy, } from './@trpc/server';
//# sourceMappingURL=shared.d.ts.map