{"version": 3, "file": "middleware.d.ts", "sourceRoot": "", "sources": ["../../src/unstable-core-do-not-import/middleware.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAC9C,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,UAAU,CAAC;AACxC,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AACjD,OAAO,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAGlE,gBAAgB;AAChB,eAAO,MAAM,gBAAgB,EAAyB,kBAAkB,GAAG;IACzE,OAAO,EAAE,kBAAkB,CAAC;CAC7B,CAAC;AACF,KAAK,gBAAgB,GAAG,OAAO,gBAAgB,CAAC;AAEhD,UAAU,oBAAoB;IAC5B;;;OAGG;IACH,QAAQ,CAAC,MAAM,EAAE,gBAAgB,CAAC;CACnC;AAED,UAAU,kBAAkB,CAAC,iBAAiB,CAAE,SAAQ,oBAAoB;IAC1E,EAAE,EAAE,IAAI,CAAC;IACT,IAAI,EAAE,OAAO,CAAC;CAEf;AAED,UAAU,qBAAqB,CAAC,iBAAiB,CAC/C,SAAQ,oBAAoB;IAC5B,EAAE,EAAE,KAAK,CAAC;IACV,KAAK,EAAE,SAAS,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAAC,iBAAiB,IAC1C,qBAAqB,CAAC,iBAAiB,CAAC,GACxC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAE1C;;GAEG;AACH,MAAM,WAAW,iBAAiB,CAChC,QAAQ,EACR,KAAK,EACL,iBAAiB,EACjB,SAAS;IAET;;OAEG;IACH,aAAa,CAAC,oBAAoB,EAChC,EAAE,EACE,kBAAkB,CAChB,QAAQ,EACR,KAAK,EACL,iBAAiB,EACjB,oBAAoB,EACpB,SAAS,CACV,GACD,iBAAiB,CACf,SAAS,CAAC,QAAQ,EAAE,iBAAiB,CAAC,EACtC,KAAK,EACL,oBAAoB,EACpB,SAAS,CACV,GACJ,iBAAiB,CAClB,QAAQ,EACR,KAAK,EACL,SAAS,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,EAClD,SAAS,CACV,CAAC;IAEF;;OAEG;IACH,YAAY,EAAE,kBAAkB,CAC9B,QAAQ,EACR,KAAK,EACL,iBAAiB,EACjB,MAAM,EACN,SAAS,CACV,EAAE,CAAC;CACL;AAED;;GAEG;AACH,MAAM,MAAM,kBAAkB,CAC5B,QAAQ,EACR,KAAK,EACL,mBAAmB,EACnB,oBAAoB,EACpB,SAAS,IACP;IACF,CAAC,IAAI,EAAE;QACL,GAAG,EAAE,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC,CAAC;QACxD,IAAI,EAAE,aAAa,CAAC;QACpB,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,SAAS,CAAC;QACjB,WAAW,EAAE,aAAa,CAAC;QAC3B,IAAI,EAAE,KAAK,GAAG,SAAS,CAAC;QACxB,MAAM,EAAE,WAAW,GAAG,SAAS,CAAC;QAChC,IAAI,EAAE;YACJ,IAAI,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACnD,CAAC,gBAAgB,EAAE,IAAI,EAAE;gBACvB,GAAG,CAAC,EAAE,gBAAgB,CAAC;gBACvB,KAAK,CAAC,EAAE,OAAO,CAAC;aACjB,GAAG,OAAO,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAChD,CAAC,IAAI,EAAE;gBACL,WAAW,EAAE,aAAa,CAAC;aAC5B,GAAG,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAC;SACpD,CAAC;KACH,GAAG,OAAO,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC,CAAC;IACpD,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC5B,CAAC;AAEF,MAAM,MAAM,qBAAqB,GAAG,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAChF,MAAM,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzE;;GAEG;AACH,wBAAgB,uBAAuB,CACrC,QAAQ,EACR,KAAK,EACL,SAAS,GAAG,OAAO,MAkBO,iBAAiB,MACrC,kBAAkB,CACpB,QAAQ,EACR,KAAK,EACL,MAAM,EACN,iBAAiB,EACjB,SAAS,CACV,KACA,iBAAiB,CAAC,QAAQ,EAAE,KAAK,EAAE,iBAAiB,EAAE,SAAS,CAAC,CAKpE;AAED;;;;GAIG;AACH,eAAO,MAAM,iCAAiC,GAC5C,IAAI,SAAS;IACX,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,OAAO,CAAC;CACjB;aAzByB,iBAAiB;aA4BrB,MAAM,CAAC,SAAS,MAAM;;cACrB,MAAM,GAAC,SAAS,MAAM;;eACrB,MAAM,GAAC;;aAFT,MAAM,CAAC,SAAS,MAAM;;cACrB,MAAM,GAAC,SAAS,MAAM;;eACrB,MAAM,GAAC;;CAE/B,CAAC;AAEH;;;GAGG;AACH,wBAAgB,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,yBA4BnE;AAED;;GAEG;AACH,wBAAgB,sBAAsB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,yBAwBtE"}