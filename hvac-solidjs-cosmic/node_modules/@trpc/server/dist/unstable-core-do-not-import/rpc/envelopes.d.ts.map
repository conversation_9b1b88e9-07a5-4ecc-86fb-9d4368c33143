{"version": 3, "file": "envelopes.d.ts", "sourceRoot": "", "sources": ["../../../src/unstable-core-do-not-import/rpc/envelopes.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AACrD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAClD,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,SAAS,CAAC;AAEtD;;GAEG;AACH,MAAM,WAAW,cAAc,CAAC,KAAK,SAAS,MAAM,GAAG,MAAM;IAC3D,IAAI,EAAE,sBAAsB,CAAC;IAC7B,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,KAAK,CAAC;CACb;AAED;;GAEG;AACH,yBAAiB,QAAQ,CAAC;IACxB,KAAY,SAAS,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;IAE/C;;OAEG;IACH,UAAiB,YAAY;QAC3B,EAAE,CAAC,EAAE,SAAS,CAAC;QACf,OAAO,CAAC,EAAE,KAAK,CAAC;KACjB;IAED,UAAiB,WAAW,CAAC,OAAO,SAAS,MAAM,GAAG,MAAM,CAC1D,SAAQ,YAAY;QACpB,MAAM,EAAE,OAAO,CAAC;KACjB;IAED,UAAiB,OAAO,CAAC,OAAO,SAAS,MAAM,GAAG,MAAM,EAAE,OAAO,GAAG,OAAO,CACzE,SAAQ,WAAW,CAAC,OAAO,CAAC;QAC5B,MAAM,EAAE,OAAO,CAAC;KACjB;IAED,UAAiB,cAAc,CAAC,OAAO,GAAG,OAAO,CAAE,SAAQ,YAAY;QACrE,MAAM,EAAE,OAAO,CAAC;KACjB;IAED,UAAiB,aAAa,CAAC,MAAM,SAAS,cAAc,GAAG,cAAc,CAC3E,SAAQ,YAAY;QACpB,KAAK,EAAE,MAAM,CAAC;KACf;CACF;AAID,MAAM,WAAW,WACf,SAAQ,QAAQ,CAAC,OAAO,CACtB,aAAa,EACb;IACE,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,OAAO,CAAC;IACf;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB,CACF;CAAG;AAEN,MAAM,WAAW,UAAU,CAAC,KAAK,GAAG,OAAO;IACzC,IAAI,EAAE,KAAK,CAAC;IACZ,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;CACb;AAED,MAAM,WAAW,mBAAmB,CAAC,KAAK,CACxC,SAAQ,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;CAAG;AAEvD,MAAM,WAAW,iBAAiB,CAChC,MAAM,SAAS,cAAc,GAAG,cAAc,CAC9C,SAAQ,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC;CAAG;AAE3C,MAAM,MAAM,YAAY,CACtB,KAAK,GAAG,OAAO,EACf,MAAM,SAAS,cAAc,GAAG,cAAc,IAC5C,iBAAiB,CAAC,MAAM,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAI3D,MAAM,MAAM,kBAAkB,GAAG,WAAW,GAAG;IAC7C,EAAE,EAAE,QAAQ,CAAC,SAAS,CAAC;CACxB,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,gCACf,SAAQ,QAAQ,CAAC,WAAW,CAAC,mBAAmB,CAAC;IACjD,EAAE,EAAE,IAAI,CAAC;CACV;AAED;;GAEG;AACH,MAAM,MAAM,yBAAyB,GAAG,gCAAgC,CAAC;AAEzE;;GAEG;AACH,MAAM,MAAM,yBAAyB,GACjC,kBAAkB,GAClB,CAAC,QAAQ,CAAC,WAAW,CAAC,mBAAmB,CAAC,GAAG;IAAE,EAAE,EAAE,QAAQ,CAAC,SAAS,CAAA;CAAE,CAAC,CAAC;AAE7E,MAAM,WAAW,iBAAiB,CAAC,KAAK,CACtC,SAAQ,QAAQ,CAAC,cAAc,CAC3B;IAAE,IAAI,EAAE,SAAS,CAAC;IAAC,IAAI,CAAC,EAAE,KAAK,CAAA;CAAE,GACjC;IAAE,IAAI,EAAE,SAAS,CAAC;IAAC,IAAI,CAAC,EAAE,KAAK,CAAA;CAAE,GACjC,UAAU,CAAC,KAAK,CAAC,CACpB;CAAG;AAEN,MAAM,MAAM,mBAAmB,CAC7B,KAAK,GAAG,OAAO,EACf,MAAM,SAAS,cAAc,GAAG,cAAc,IAC5C;IAAE,EAAE,EAAE,QAAQ,CAAC,SAAS,CAAA;CAAE,GAAG,CAC7B,iBAAiB,CAAC,MAAM,CAAC,GACzB,iBAAiB,CAAC,KAAK,CAAC,CAC3B,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,yBACf,SAAQ,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC;IACzC,EAAE,EAAE,QAAQ,CAAC,SAAS,CAAC;CACxB;AAED;;GAEG;AACH,MAAM,MAAM,yBAAyB,GAAG,yBAAyB,CAAC;AAElE;;GAEG;AACH,MAAM,MAAM,yBAAyB,CACnC,OAAO,GAAG,OAAO,EACjB,MAAM,SAAS,cAAc,GAAG,cAAc,IAC5C,yBAAyB,GAAG,mBAAmB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAErE;;GAEG;AACH,MAAM,WAAW,2BACf,SAAQ,QAAQ,CAAC,WAAW,CAAC,kBAAkB,CAAC;IAChD,IAAI,EAAE,eAAe,CAAC,kBAAkB,CAAC,CAAC;CAC3C"}