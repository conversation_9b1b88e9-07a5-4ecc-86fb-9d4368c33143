{"version": 3, "file": "inferrable.d.ts", "sourceRoot": "", "sources": ["../../../src/unstable-core-do-not-import/clientish/inferrable.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAElD,MAAM,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,EAAE,YAAY,GAAG,aAAa,CAAC,CAAC;AAE9E;;GAEG;AACH,KAAK,QAAQ,GAAG;IACd,OAAO,EAAE;QACP,MAAM,EAAE,cAAc,CAAC;KACxB,CAAC;CACH,CAAC;AAEF;;GAEG;AACH,KAAK,UAAU,GAAG;IAChB,IAAI,EAAE,QAAQ,CAAC;CAChB,CAAC;AAEF;;GAEG;AACH,KAAK,cAAc,GAAG;IACpB,MAAM,EAAE,cAAc,CAAC;CACxB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAC7B,UAAU,GACV,QAAQ,GACR,cAAc,GACd,cAAc,CAAC;AAEnB,KAAK,SAAS,CAAC,CAAC,SAAS,cAAc,IAAI;IACzC,WAAW,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC;IAC9B,UAAU,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC;CAC7B,CAAC;AACF;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAAC,WAAW,SAAS,qBAAqB,IACpE,WAAW,SAAS,cAAc,GAC9B,SAAS,CAAC,WAAW,CAAC,GACtB,WAAW,SAAS,cAAc,GAChC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,GAChC,WAAW,SAAS,QAAQ,GAC1B,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,GAC3C,WAAW,SAAS,UAAU,GAC5B,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,GACnD,KAAK,CAAC"}