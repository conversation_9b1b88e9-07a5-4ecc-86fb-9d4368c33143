{"version": 3, "file": "inference.d.ts", "sourceRoot": "", "sources": ["../../../src/unstable-core-do-not-import/clientish/inference.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAC7D,OAAO,KAAK,EACV,YAAY,EACZ,mBAAmB,EACnB,oBAAoB,EACrB,MAAM,cAAc,CAAC;AACtB,OAAO,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,WAAW,CAAC;AACzD,OAAO,KAAK,EACV,cAAc,EACd,gBAAgB,EAChB,qBAAqB,EACtB,MAAM,cAAc,CAAC;AACtB,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAE7C;;GAEG;AAEH,MAAM,MAAM,+BAA+B,CACzC,WAAW,SAAS,qBAAqB,EACzC,UAAU,SAAS,YAAY,IAC7B,gBAAgB,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC,SAAS,KAAK,GAC1D,SAAS,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,GAC3C,oBAAoB,CAAC,UAAU,CAAC,CAAC;AACrC,gBAAgB;AAEhB,MAAM,MAAM,kCAAkC,CAC5C,WAAW,SAAS,qBAAqB,EACzC,UAAU,SAAS,YAAY,IAC7B,gBAAgB,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC,SAAS,KAAK,GAC1D,SAAS,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,CAAC,GACjE,oBAAoB,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,CAAC;AAE3D,MAAM,MAAM,mBAAmB,CAC7B,KAAK,SAAS,OAAO,GAAG,QAAQ,EAChC,KAAK,SAAS,cAAc,EAC5B,OAAO,SAAS,YAAY,IAC1B;KACD,IAAI,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,MAAM,MAAM,GACvD,MAAM,SAAS,YAAY,GACzB,KAAK,SAAS,OAAO,GACnB,mBAAmB,CAAC,MAAM,CAAC,GAC3B,+BAA+B,CAAC,KAAK,EAAE,MAAM,CAAC,GAChD,MAAM,SAAS,YAAY,GACzB,mBAAmB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,GACzC,KAAK,GACT,KAAK;CACV,CAAC;AAEF,MAAM,MAAM,iBAAiB,CAAC,OAAO,SAAS,SAAS,IAAI,mBAAmB,CAC5E,OAAO,EACP,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,EACpC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAC1B,CAAC;AAEF,MAAM,MAAM,kBAAkB,CAAC,OAAO,SAAS,SAAS,IAAI,mBAAmB,CAC7E,QAAQ,EACR,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,EACpC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAC1B,CAAC"}