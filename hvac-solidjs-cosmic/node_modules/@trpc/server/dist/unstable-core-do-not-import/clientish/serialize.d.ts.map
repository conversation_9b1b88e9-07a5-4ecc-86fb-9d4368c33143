{"version": 3, "file": "serialize.d.ts", "sourceRoot": "", "sources": ["../../../src/unstable-core-do-not-import/clientish/serialize.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,QAAQ,EAAE,qBAAqB,EAAE,MAAM,UAAU,CAAC;AAEhE;;GAEG;AACH,KAAK,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;AAEtD,KAAK,gBAAgB,GAAG,QAAQ,GAAG,MAAM,GAAG,SAAS,CAAC;AAKtD,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;AAK/C,KAAK,cAAc,GAAG,aAAa,GAAG,SAAS,CAAC;AAEhD,KAAK,QAAQ,CAAC,CAAC,SAAS,MAAM,IAAI,MAAM,qBAAqB,CAAC,CAAC,CAAC,SAAS,KAAK,GAC1E,IAAI,GACJ,KAAK,CAAC;AAGV,MAAM,MAAM,SAAS,CAAC,CAAC,IACrB,KAAK,CAAC,CAAC,CAAC,SAAS,IAAI,GAAG,GAAG,GAC3B,OAAO,SAAS,CAAC,GAAG,OAAO,GAC3B,CAAC,SAAS,aAAa,CAAC,MAAM,EAAE,EAAE,MAAM,OAAO,EAAE,MAAM,KAAK,CAAC,GAAG,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,GAClI,CAAC,SAAS,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GACxD,CAAC,SAAS,cAAc,GAAG,CAAC,GAC5B,CAAC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,GAC3C,CAAC,SAAS,gBAAgB,GAAG,KAAK,GAClC,CAAC,SAAS;IAAE,MAAM,IAAI,MAAM,CAAC,CAAA;CAAE,GAAG,CAAC,GACnC,CAAC,SAAS,EAAE,GAAG,EAAE,GACjB,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,GACrD,CAAC,SAAS,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,SAAS,gBAAgB,GAAG,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,GACrF,CAAC,SAAS,MAAM,GACd,QAAQ,CAAC,CAAC,CAAC,SAAS,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GACjE,QAAQ,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,GACnD,KAAK,CAAC;AAER,uGAAuG;AACvG,KAAK,cAAc,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC,IAAI;KACtD,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,GAAG,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACvE,CAAC;AAGF,KAAK,kBAAkB,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAEnD,CAAC,SAAS,MAAM,GAAG,KAAK,GAExB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,GAAG,CAAC,GAE5B,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAExB,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,GAAG,KAAK,GAErC,CAAC,CAAC;AACJ;;;IAGI;AACJ,MAAM,MAAM,eAAe,CAAC,CAAC,SAAS,MAAM,IAAI;KAC7C,CAAC,IAAI,MAAM,CAAC,IAAI,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC5D,CAAC;AAEF;;;GAGG;AACH,KAAK,iBAAiB,CAAC,CAAC,SAAS,MAAM,IAAI,OAAO,CAChD;KACG,CAAC,IAAI,MAAM,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC;CACnD,CAAC,MAAM,CAAC,CAAC,EACV,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,KAAK,0BAA0B,GAAG;IAAE,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAA;CAAE,SAAS;IAC9D,CAAC,CAAC,EAAE,CAAC,CAAC;CACP,GACG,KAAK,GACL,IAAI,CAAC;AAET;;GAEG;AACH,KAAK,iBAAiB,CAAC,CAAC,SAAS,MAAM,IAAI,MAAM,SAAS,MAAM,CAAC,GAC7D,IAAI,GACJ,KAAK,CAAC;AAEV;;GAEG;AACH,KAAK,oBAAoB,CAAC,CAAC,SAAS,MAAM,IAAI;KAC3C,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,qBAAqB,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAC3D,CAAC,CAAC,CAAC,CAAC,EACJ,SAAS,CACV;CACF,CAAC;AAEF;;;GAGG;AACH,KAAK,eAAe,CAAC,CAAC,SAAS,MAAM,IAAI;KACtC,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;CACtE,CAAC;AAEF;;GAEG;AACH,KAAK,mBAAmB,CAAC,CAAC,SAAS,MAAM,IAEvC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,GAEzE,CAAC,0BAA0B,SAAS,IAAI,GACpC,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,GACnE,iBAAiB,CAAC,CAAC,CAAC,SAAS,IAAI,GAC/B,oBAAoB,CAAC,CAAC,CAAC,GACvB,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC"}