{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../src/unstable-core-do-not-import/types.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;GAEG;AACH,MAAM,MAAM,KAAK,CAAC,KAAK,IAAI,KAAK,GAAG,IAAI,GAAG,SAAS,CAAC;AAEpD;;;GAGG;AACH,MAAM,MAAM,QAAQ,CAAC,KAAK,IAAI,KAAK,SAAS,GAAG,EAAE,GAAG,IAAI,GACpD,KAAK,GACL;KAAG,CAAC,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;CAAE,CAAC;AAErC;;GAEG;AACH,MAAM,MAAM,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,SAAS,CAAC,CAAC;AAE5D;;GAEG;AACH,MAAM,MAAM,YAAY,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAEzD,MAAM,MAAM,UAAU,CAAC,IAAI,SAAS,MAAM,EAAE,OAAO,IAAI;KACpD,IAAI,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,OAAO,GAAG,IAAI,GAAG,KAAK;CAChE,CAAC,MAAM,IAAI,CAAC,CAAC;AAEd;;GAEG;AACH,MAAM,MAAM,MAAM,CAAC,KAAK,EAAE,IAAI,GAAG,OAAO,IACpC;IAAE,EAAE,EAAE,IAAI,CAAC;IAAC,KAAK,EAAE,KAAK,CAAA;CAAE,GAC1B;IAAE,EAAE,EAAE,KAAK,CAAC;IAAC,KAAK,EAAE,IAAI,CAAA;CAAE,CAAC;AAE/B;;GAEG;AACH,MAAM,MAAM,MAAM,CAAC,IAAI,SAAS,MAAM,EAAE,OAAO,IAAI,IAAI,CACrD,IAAI,EACJ,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAC1B,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,MAAM,CAAC,KAAK,IAAI,KAAK,SAAS,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,MAAM,CAAC,GACjE,OAAO,CAAC,CAAC,CAAC,GACV,KAAK,CAAC;AAEV;;;GAGG;AACH,MAAM,MAAM,WAAW,CAAC,OAAO,IAAI,OAAO,SAAS,MAAM,GACrD;KACG,CAAC,IAAI,MAAM,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;CAC/C,GACD,OAAO,CAAC;AAEZ;;;GAGG;AACH,MAAM,MAAM,gBAAgB,CAAC,IAAI,EAAE,IAAI,SAAS,MAAM,GAAG,IAAI,IAAI,SAAS,GAAG,GACzE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAChB,KAAK,CAAC;AAEV;;;;;;GAMG;AACH,MAAM,MAAM,qBAAqB,CAAC,IAAI,IAAI;KACvC,CAAC,IAAI,MAAM,IAAI,IAAI,MAAM,SAAS,CAAC,GAChC,KAAK,GACL,MAAM,SAAS,CAAC,GACd,KAAK,GACL,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;CAClB,CAAC;AAEF;;;;;GAKG;AACH,MAAM,MAAM,SAAS,CAAC,KAAK,EAAE,KAAK,IAAI,KAAK,SAAS,GAAG,GACnD,KAAK,SAAS,MAAM,GAClB;KACG,CAAC,IACE,MAAM,qBAAqB,CAAC,KAAK,CAAC,GAClC,MAAM,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,MAAM,KAAK,GAC1D,KAAK,CAAC,CAAC,CAAC,GACR,CAAC,SAAS,MAAM,KAAK,GACnB,KAAK,CAAC,CAAC,CAAC,GACR,KAAK;CACZ,GAAG,CAAC,MAAM,SAAS,MAAM,KAAK,GAC3B;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAA;CAAE,GAChC,MAAM,SAAS,MAAM,KAAK,GACxB;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAA;CAAE,GAChC,EAAE,CAAC,GACT,KAAK,GACP,KAAK,CAAC;AAEV;;GAEG;AACH,MAAM,MAAM,aAAa,CAAC,YAAY,EAAE,cAAc,IACpD,YAAY,SAAS,cAAc,GAC/B,OAAO,CAAC,MAAM,YAAY,EAAE,MAAM,cAAc,CAAC,SAAS,KAAK,GAC7D,YAAY,GACZ,cAAc,GAChB,KAAK,CAAC;AAEZ;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAAC,KAAK,EAAE,KAAK,IAAI,SAAS,SAAS,KAAK,GAChE,SAAS,SAAS,KAAK,GACrB,KAAK,GACL,KAAK,GACP,KAAK,CAAC;AAEV,MAAM,MAAM,YAAY,CACtB,MAAM,EACN,KAAK,SAAS,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,IAC5C;KACD,CAAC,IAAI,MAAM,KAAK,GAAG,MAAM,SAAS,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK;CACxD,CAAC,MAAM,KAAK,CAAC,CAAC;AAEf,MAAM,MAAM,cAAc,CAAC,KAAK,SAAS,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI;KAC1E,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;CAC5D,CAAC;AAEF;;;;GAIG;AAEH;;GAEG;AACH,MAAM,MAAM,iBAAiB,CAAC,IAAI,SAAS,MAAM,IAC/C,iBAAiB,IAAI,oGAAoG,CAAC;AAE5H;;GAEG;AACH,MAAM,MAAM,qBAAqB,CAAC,KAAK,EAAE,KAAK,IAAI,MAAM,KAAK,GAC3D,MAAM,KAAK,SAAS,KAAK,GACvB,KAAK,GAAG,KAAK,GACb,iBAAiB,CAAC,MAAM,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,CAAC;AAE1D;;;GAGG;AACH,MAAM,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC;AAEnD,QAAA,MAAM,YAAY,eAAW,CAAC;AAC9B,MAAM,MAAM,WAAW,GAAG,OAAO,YAAY,CAAC;AAC9C,MAAM,MAAM,SAAS,CAAC,QAAQ,SAAS,MAAM,IAAI,QAAQ,GAAG;IAC1D,CAAC,EAAE,OAAO,YAAY,CAAC;CACxB,CAAC;AACF,MAAM,MAAM,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;AAE7C,MAAM,MAAM,0BAA0B,CAAC,MAAM,IAC3C,MAAM,SAAS,aAAa,CAAC,MAAM,SAAS,CAAC,GAAG,SAAS,EAAE,GAAG,MAAM,CAAC;AAEvE;;;GAGG;AACH,MAAM,MAAM,uBAAuB,CAAC,CAAC,IACnC,CAAC,SAAS,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC"}