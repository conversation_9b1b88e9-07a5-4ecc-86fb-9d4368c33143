{"version": 3, "file": "rootConfig.d.ts", "sourceRoot": "", "sources": ["../../src/unstable-core-do-not-import/rootConfig.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,uBAAuB,EAAE,MAAM,gCAAgC,CAAC;AAC9E,OAAO,KAAK,EAAE,iBAAiB,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAC3E,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;AAC3D,OAAO,KAAK,EAAE,wBAAwB,EAAE,MAAM,cAAc,CAAC;AAE7D;;;GAGG;AACH,MAAM,WAAW,SAAS;IACxB,GAAG,EAAE,MAAM,CAAC;IACZ,IAAI,EAAE,MAAM,CAAC;IACb,UAAU,EAAE,iBAAiB,CAAC;IAC9B,WAAW,EAAE,OAAO,CAAC;CACtB;AAED;;GAEG;AACH,eAAO,MAAM,eAAe,EAAE,OAMmB,CAAC;AAElD;;;GAGG;AACH,MAAM,WAAW,UAAU,CAAC,MAAM,SAAS,SAAS;IAClD;;;OAGG;IACH,MAAM,EAAE,MAAM,CAAC;IACf;;;OAGG;IACH,WAAW,EAAE,uBAAuB,CAAC;IACrC;;;OAGG;IACH,cAAc,EAAE,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;IACpE;;;;OAIG;IACH,oBAAoB,EAAE,OAAO,CAAC;IAC9B;;;;OAIG;IACH,QAAQ,EAAE,OAAO,CAAC;IAClB;;;;OAIG;IACH,KAAK,EAAE,OAAO,CAAC;IAEf,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;IAErE;;;OAGG;IACH,GAAG,CAAC,EAAE;QACJ;;;WAGG;QACH,OAAO,CAAC,EAAE,OAAO,CAAC;KACnB,GAAG,IAAI,CACN,wBAAwB,EACxB,MAAM,GAAG,uBAAuB,GAAG,eAAe,GAAG,QAAQ,CAC9D,CAAC;IAEF;;;OAGG;IACH,KAAK,CAAC,EAAE,IAAI,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;IAC7C,YAAY,CAAC,EAAE,EAAE,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,MAAM,eAAe,CAAC,SAAS,SAAS,SAAS,IAAI,SAAS,CAAC;AAErE,MAAM,MAAM,YAAY,GAAG,eAAe,CAAC;IACzC,GAAG,EAAE,GAAG,CAAC;IACT,IAAI,EAAE,GAAG,CAAC;IACV,UAAU,EAAE,GAAG,CAAC;IAChB,WAAW,EAAE,GAAG,CAAC;CAClB,CAAC,CAAC;AAEH,KAAK,SAAS,CAAC,UAAU,SAAS,OAAO,EAAE,KAAK,IAAI,UAAU,SAAS,IAAI,GACvE,OAAO,CAAC,KAAK,CAAC,GACd,KAAK,CAAC;AAEV;;;GAGG;AACH,MAAM,MAAM,qBAAqB,CAC/B,QAAQ,EACR,SAAS,SAAS,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,IACvC,SAAS,CACX,MAAM,SAAS,QAAQ,GAAG,IAAI,GAAG,KAAK,EACtC;IACE;;QAEI;IACJ,aAAa,EAAE,SAAS,CAAC;CAC1B,CACF,CAAC"}