import type { ProcedureType } from '../procedure';
import type { TRPC_ERROR_CODE_KEY, TRPC_ERROR_CODE_NUMBER, TRPCErrorShape } from '../rpc';
import type { TRPCError } from './TRPCError';
/**
 * @internal
 */
export type ErrorFormatter<TContext, TShape extends TRPCErrorShape> = (opts: {
    error: TRPCError;
    type: ProcedureType | 'unknown';
    path: string | undefined;
    input: unknown;
    ctx: TContext | undefined;
    shape: DefaultErrorShape;
}) => TShape;
/**
 * @internal
 */
export type DefaultErrorData = {
    code: TRPC_ERROR_CODE_KEY;
    httpStatus: number;
    /**
     * Path to the procedure that threw the error
     */
    path?: string;
    /**
     * Stack trace of the error (only in development)
     */
    stack?: string;
};
/**
 * @internal
 */
export interface DefaultErrorShape extends TRPCErrorShape<DefaultErrorData> {
    message: string;
    code: TRPC_ERROR_CODE_NUMBER;
}
export declare const defaultFormatter: ErrorFormatter<any, any>;
//# sourceMappingURL=formatter.d.ts.map