{"version": 3, "file": "router.d.ts", "sourceRoot": "", "sources": ["../../src/unstable-core-do-not-import/router.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAGhD,OAAO,EAA2B,SAAS,EAAE,MAAM,mBAAmB,CAAC;AACvE,OAAO,KAAK,EACV,YAAY,EACZ,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,qCAAqC,EACtC,MAAM,aAAa,CAAC;AACrB,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,oBAAoB,CAAC;AAC/D,OAAO,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE7D,OAAO,KAAK,EAAE,YAAY,EAAW,MAAM,SAAS,CAAC;AAQrD,MAAM,WAAW,YAAY;IAC3B,CAAC,GAAG,EAAE,MAAM,GAAG,YAAY,GAAG,YAAY,CAAC;CAC5C;AAED,KAAK,iBAAiB,CAAC,UAAU,SAAS,YAAY,IAAI,CACxD,KAAK,EAAE,mBAAmB,CAAC,UAAU,CAAC,KACnC,OAAO,CACV,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,cAAc,GAC7C,UAAU,SAAS,qCAAqC,CAAC,GAAG,CAAC,GAC3D,UAAU,CAAC,oBAAoB,CAAC,UAAU,CAAC,EAAE,SAAS,CAAC,GACvD,oBAAoB,CAAC,UAAU,CAAC,GAClC,oBAAoB,CAAC,UAAU,CAAC,CACrC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,oBAAoB,CAAC,OAAO,SAAS,YAAY,IAAI;KAC9D,IAAI,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,MAAM,MAAM,GACvD,MAAM,SAAS,YAAY,GACzB,iBAAiB,CAAC,MAAM,CAAC,GACzB,MAAM,SAAS,YAAY,GACzB,oBAAoB,CAAC,MAAM,CAAC,GAC5B,KAAK,GACT,KAAK;CACV,CAAC;AAEF;;GAEG;AAEH,MAAM,MAAM,wBAAwB,CAAC,QAAQ,IAAI,CAC/C,IAAI,EAAE,mBAAmB,CAAC,QAAQ,CAAC,KAChC,IAAI,CAAC;AAEV;;GAEG;AACH,MAAM,MAAM,YAAY,CACtB,KAAK,SAAS,YAAY,EAC1B,OAAO,SAAS,YAAY,IAC1B;AACF;;;;GAIG;AACH,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EACtD,OAAO,CAAC,EAAE;IACR,OAAO,CAAC,EAAE,wBAAwB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IACjD,MAAM,CAAC,EAAE,WAAW,CAAC;CACtB,KACE,oBAAoB,CAAC,OAAO,CAAC,CAAC;AAEnC,QAAA,MAAM,UAAU,eAAiB,CAAC;AAClC,MAAM,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG;IAAE,CAAC,UAAU,CAAC,EAAE,IAAI,CAAA;CAAE,CAAC;AAExE,KAAK,UAAU,CAAC,IAAI,IAAI;IACtB,IAAI,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1B,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;CACjB,CAAC;AAaF;;;GAGG;AACH,wBAAgB,IAAI,CAAC,OAAO,SAAS,SAAS,EAC5C,YAAY,EAAE,MAAM,OAAO,CACvB,OAAO,GACP;IACE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;CACxB,CACJ,GACA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAsBxB;AAMD,MAAM,WAAW,MAAM,CACrB,KAAK,SAAS,YAAY,EAC1B,OAAO,SAAS,YAAY;IAE5B,IAAI,EAAE;QACJ,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3B,MAAM,EAAE,IAAI,CAAC;QACb,SAAS,CAAC,EAAE,KAAK,CAAC;QAClB,UAAU,EAAE,OAAO,CAAC;QACpB,MAAM,EAAE,OAAO,CAAC;QAChB,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;KAC7C,CAAC;IACF;;OAEG;IACH,YAAY,EAAE,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CAC5C;AAED,MAAM,MAAM,WAAW,CACrB,KAAK,SAAS,YAAY,EAC1B,IAAI,SAAS,YAAY,IACvB,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;AAE/B,MAAM,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAEzC,MAAM,MAAM,oBAAoB,CAAC,OAAO,SAAS,SAAS,IACxD,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC;AAEvC,MAAM,MAAM,kBAAkB,CAAC,OAAO,SAAS,SAAS,IACtD,oBAAoB,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC;AACvC,MAAM,MAAM,gBAAgB,CAAC,OAAO,SAAS,SAAS,IACpD,oBAAoB,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC;AAC9C,MAAM,MAAM,eAAe,CAAC,OAAO,SAAS,SAAS,IACnD,oBAAoB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC;AAmCxC,MAAM,MAAM,mBAAmB,GAAG;IAChC,CAAC,GAAG,EAAE,MAAM,GACR,YAAY,GACZ,SAAS,GACT,mBAAmB,GACnB,IAAI,CAAC,SAAS,CAAC,CAAC;CACrB,CAAC;AAEF,MAAM,MAAM,2BAA2B,CACrC,cAAc,SAAS,mBAAmB,IACxC;KACD,CAAC,IAAI,MAAM,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,SAAS,MAAM,MAAM,GAC/D,MAAM,SAAS,YAAY,GACzB,MAAM,GACN,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE,MAAM,OAAO,CAAC,GACvC,OAAO,GACP,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,OAAO,CAAC,CAAC,GAC7C,OAAO,GACP,MAAM,SAAS,mBAAmB,GAChC,2BAA2B,CAAC,MAAM,CAAC,GACnC,KAAK,GACb,KAAK;CACV,CAAC;AAEF;;GAEG;AACH,wBAAgB,mBAAmB,CAAC,KAAK,SAAS,YAAY,EAC5D,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,IAEE,MAAM,SAAS,mBAAmB,SACpD,MAAM,KACZ,WAAW,CAAC,KAAK,EAAE,2BAA2B,CAAC,MAAM,CAAC,CAAC,CAyG3D;AAQD;;GAEG;AACH,wBAAsB,kBAAkB,CACtC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,EACtC,IAAI,EAAE,MAAM,GACX,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,CAoB9B;AAED;;GAEG;AACH,wBAAsB,aAAa,CACjC,IAAI,EAAE,oBAAoB,CAAC,OAAO,CAAC,GAAG;IACpC,MAAM,EAAE,SAAS,CAAC;IAClB,mBAAmB,CAAC,EAAE,OAAO,CAAC;CAC/B,gBA4BF;AAED,wBAAgB,mBAAmB,CAAC,KAAK,SAAS,YAAY,MAC1B,OAAO,SAAS,YAAY,EAC5D,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,KAC3C,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAgDhC;AAED,gBAAgB;AAChB,KAAK,YAAY,CACf,QAAQ,SAAS,SAAS,EAAE,EAC5B,KAAK,SAAS,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,EACrE,OAAO,SAAS,YAAY,GAAG,EAAE,IAC/B,QAAQ,SAAS;IACnB,MAAM,IAAI,SAAS,SAAS;IAC5B,GAAG,MAAM,IAAI,SAAS,SAAS,EAAE;CAClC,GACG,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,GAC3D,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAEhC,wBAAgB,YAAY,CAAC,QAAQ,SAAS,SAAS,EAAE,EACvD,GAAG,UAAU,EAAE,CAAC,GAAG,QAAQ,CAAC,GAC3B,YAAY,CAAC,QAAQ,CAAC,CAoDxB"}