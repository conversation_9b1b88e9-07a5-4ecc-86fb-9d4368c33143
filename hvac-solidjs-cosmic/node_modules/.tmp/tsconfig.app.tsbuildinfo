{"root": ["../../src/App.tsx", "../../src/index.tsx", "../../src/vite-env.d.ts", "../../src/components/atoms/CosmicCard.tsx", "../../src/components/atoms/GoldenButton.tsx", "../../src/components/molecules/CosmicBreadcrumb.tsx", "../../src/components/molecules/CosmicForm.tsx", "../../src/components/molecules/CosmicModal.tsx", "../../src/components/molecules/CosmicToast.tsx", "../../src/components/molecules/DataTable.tsx", "../../src/components/molecules/StatsGrid.tsx", "../../src/components/organisms/CustomerDashboard.tsx", "../../src/components/organisms/ServiceOrderBoard.tsx", "../../src/components/pages/AnalyticsPage.tsx", "../../src/components/pages/CustomersPage.tsx", "../../src/components/pages/DashboardPage.tsx", "../../src/components/pages/HomePage.tsx", "../../src/components/pages/InventoryPage.tsx", "../../src/components/pages/ServiceOrdersPage.tsx", "../../src/components/pages/SettingsPage.tsx", "../../src/components/templates/MainLayout.tsx", "../../src/lib/api/client.ts", "../../src/lib/api/hooks.ts", "../../src/lib/api/index.ts", "../../src/lib/api/types.ts"], "errors": true, "version": "5.8.3"}